/**
 * Status接口优化效果测试工具
 * 用于验证优化后的性能提升
 */

import { getStatusDataManager } from './statusDataManager.js'
import SecureLogger from './secureLogger.js'

class StatusOptimizationTest {
  constructor() {
    this.statusManager = getStatusDataManager()
    this.testResults = []
  }

  /**
   * 测试缓存命中率
   */
  async testCacheHitRate() {
    const testName = '缓存命中率测试'
    console.log(`[StatusTest] 开始${testName}`)
    
    const startTime = Date.now()
    let cacheHits = 0
    let totalRequests = 0
    
    // 连续请求10次，测试缓存效果
    for (let i = 0; i < 10; i++) {
      try {
        const result = await this.statusManager.getStatusData({
          mode: 'cache_first',
          params: {},
          silent: true
        })
        
        totalRequests++
        if (result.fromCache) {
          cacheHits++
        }
        
        // 间隔100ms
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.warn(`[StatusTest] 第${i+1}次请求失败:`, error)
      }
    }
    
    const endTime = Date.now()
    const hitRate = totalRequests > 0 ? (cacheHits / totalRequests * 100).toFixed(2) : 0
    
    const result = {
      testName,
      totalRequests,
      cacheHits,
      hitRate: `${hitRate}%`,
      duration: `${endTime - startTime}ms`,
      success: hitRate > 50 // 期望缓存命中率超过50%
    }
    
    this.testResults.push(result)
    console.log(`[StatusTest] ${testName}完成:`, result)
    return result
  }

  /**
   * 测试防抖效果
   */
  async testDebounceEffect() {
    const testName = '防抖效果测试'
    console.log(`[StatusTest] 开始${testName}`)
    
    const startTime = Date.now()
    let actualRequests = 0
    
    // 监听实际的网络请求
    const originalRequest = this.statusManager.fetchFromServer
    this.statusManager.fetchFromServer = async (...args) => {
      actualRequests++
      return await originalRequest.apply(this.statusManager, args)
    }
    
    // 快速连续发起5次请求
    const promises = []
    for (let i = 0; i < 5; i++) {
      promises.push(
        this.statusManager.getStatusData({
          mode: 'force_refresh',
          params: {},
          silent: true
        })
      )
    }
    
    await Promise.allSettled(promises)
    
    // 恢复原始方法
    this.statusManager.fetchFromServer = originalRequest
    
    const endTime = Date.now()
    
    const result = {
      testName,
      expectedRequests: 5,
      actualRequests,
      debounceEffective: actualRequests < 5,
      duration: `${endTime - startTime}ms`,
      success: actualRequests <= 2 // 期望防抖后实际请求数不超过2次
    }
    
    this.testResults.push(result)
    console.log(`[StatusTest] ${testName}完成:`, result)
    return result
  }

  /**
   * 测试缓存统计
   */
  testCacheStats() {
    const testName = '缓存统计测试'
    console.log(`[StatusTest] 开始${testName}`)
    
    const stats = this.statusManager.getCacheStats()
    
    const result = {
      testName,
      ...stats,
      cacheEfficiency: stats.total > 0 ? `${(stats.valid / stats.total * 100).toFixed(2)}%` : '0%',
      success: stats.valid > 0 // 期望有有效缓存
    }
    
    this.testResults.push(result)
    console.log(`[StatusTest] ${testName}完成:`, result)
    return result
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('[StatusTest] 开始运行所有优化效果测试')
    
    try {
      // 清空之前的测试结果
      this.testResults = []
      
      // 运行各项测试
      await this.testCacheHitRate()
      await this.testDebounceEffect()
      this.testCacheStats()
      
      // 生成测试报告
      const report = this.generateReport()
      console.log('[StatusTest] 测试报告:', report)
      
      return report
    } catch (error) {
      console.error('[StatusTest] 测试运行失败:', error)
      return {
        success: false,
        error: error.message,
        results: this.testResults
      }
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.success).length
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0
    
    return {
      summary: {
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        successRate: `${successRate}%`,
        overallSuccess: successRate >= 80 // 期望80%以上的测试通过
      },
      details: this.testResults,
      recommendations: this.generateRecommendations()
    }
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = []
    
    const cacheTest = this.testResults.find(r => r.testName.includes('缓存命中率'))
    if (cacheTest && parseFloat(cacheTest.hitRate) < 70) {
      recommendations.push('建议增加缓存时间或优化缓存策略')
    }
    
    const debounceTest = this.testResults.find(r => r.testName.includes('防抖效果'))
    if (debounceTest && !debounceTest.debounceEffective) {
      recommendations.push('建议调整防抖延迟时间')
    }
    
    const statsTest = this.testResults.find(r => r.testName.includes('缓存统计'))
    if (statsTest && statsTest.valid === 0) {
      recommendations.push('建议检查缓存机制是否正常工作')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('优化效果良好，无需额外调整')
    }
    
    return recommendations
  }
}

// 导出测试工具
export default StatusOptimizationTest

// 提供便捷的测试方法
export async function runStatusOptimizationTest() {
  const tester = new StatusOptimizationTest()
  return await tester.runAllTests()
}

// 在控制台中可以直接调用的测试方法
if (typeof window !== 'undefined') {
  window.testStatusOptimization = runStatusOptimizationTest
}
