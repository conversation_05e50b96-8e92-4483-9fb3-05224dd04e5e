// 工单管理页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    activeTab: 0,
    tabs: [
      { key: 'repair', title: '报修单' },
      { key: 'complaint', title: '投诉建议' }
    ],
    repairList: [],
    complaintList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,
    filters: {
      status: '',
      startDate: '',
      endDate: ''
    }
  },

  onLoad(options) {
    console.log('工单管理页面加载')

    // 处理从工作台传来的activeTab参数
    if (options.activeTab) {
      const tabIndex = this.data.tabs.findIndex(tab => tab.key === options.activeTab)
      if (tabIndex !== -1) {
        this.setData({ activeTab: tabIndex })
      }
    }
  },

  onShow() {
    handlePropertyPageShow(this, this.loadCurrentTabData)
  },

  // 加载当前Tab的数据
  loadCurrentTabData() {
    const currentTab = this.data.tabs[this.data.activeTab]
    if (currentTab.key === 'repair') {
      this.loadRepairList(true)
    } else {
      this.loadComplaintList(true)
    }
  },

  // Tab切换
  onTabChange(e) {
    const index = e.detail.index
    this.setData({
      activeTab: index,
      pageNum: 1,
      hasMore: true
    })
    this.loadCurrentTabData()
  },

  // 加载报修列表
  async loadRepairList(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })
    if (refresh) {
      this.setData({ pageNum: 1, hasMore: true })
    }

    try {
      const res = await app.request({
        url: '/api/wx/property/repair/list',
        method: 'POST',
        data: {
          pageNum: this.data.pageNum,
          pageSize: this.data.pageSize,
          ...this.data.filters
        }
      })

      if (res.code === 0) {
        const newList = res.data.list || []
        this.setData({
          repairList: refresh ? newList : [...this.data.repairList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          pageNum: this.data.pageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载报修列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  // 加载投诉列表
  async loadComplaintList(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })
    if (refresh) {
      this.setData({ pageNum: 1, hasMore: true })
    }

    try {
      const res = await app.request({
        url: '/api/wx/property/complaint/list',
        method: 'POST',
        data: {
          pageNum: this.data.pageNum,
          pageSize: this.data.pageSize,
          ...this.data.filters
        }
      })

      if (res.code === 0) {
        const newList = res.data.list || []
        this.setData({
          complaintList: refresh ? newList : [...this.data.complaintList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          pageNum: this.data.pageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载投诉列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    const currentTab = this.data.tabs[this.data.activeTab]
    wx.navigateTo({
      url: `/pages/property/orders/detail?id=${id}&type=${currentTab.key}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadCurrentTabData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCurrentTabData()
    }
  }
})
