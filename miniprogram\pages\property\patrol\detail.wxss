/* 巡更记录详情页面样式 */
.patrol-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 主要内容 */
.main-content {
  padding: 20rpx;
}

/* 通用卡片样式 */
.info-card,
.location-card,
.photo-card,
.remark-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 基本信息卡片 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.location-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-overdue {
  background: #f8d7da;
  color: #721c24;
}

.location-address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 卡片标题 */
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.photo-count {
  font-size: 24rpx;
  color: #667eea;
  margin-left: 8rpx;
}

/* 位置信息 */
.location-info {
  margin-bottom: 24rpx;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.location-item:last-child {
  margin-bottom: 0;
}

.location-item .label {
  color: #666;
  margin-right: 16rpx;
  min-width: 140rpx;
}

.location-item .value {
  color: #333;
  font-weight: 500;
  flex: 1;
}

.distance-normal {
  color: #52c41a;
}

.distance-warning {
  color: #f5222d;
}

/* 查看位置按钮 */
.view-location-btn {
  width: 100%;
  height: 80rpx;
  background: #f0f3ff;
  color: #667eea;
  border: 1rpx solid #667eea;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* 照片网格 */
.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}
