# 巡更配置编辑页面模板修复

## 问题描述
编辑页面出现 Thymeleaf 模板解析错误：
```
org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/oc/patrol/edit.html]")
```

## 问题原因
编辑页面使用了 Thymeleaf 标签（如 `th:value`、`th:checked`、`th:text`）来绑定数据，但项目中其他页面使用 `renderForm` 方法来动态加载数据。

## 修复方案
参考 `maintain.html` 页面的实现方式，将 Thymeleaf 数据绑定改为 `renderForm` 动态加载。

## 修复内容

### 1. 移除 Thymeleaf 数据绑定标签

**修复前**：
```html
<input name="config_id" th:value="${patrolConfig.config_id}" type="hidden">
<input name="location_name" th:value="${patrolConfig.location_name}" class="form-control" type="text" required>
<input name="planned_time" th:value="${patrolConfig.planned_time}" class="form-control" type="time" required>
<input type="radio" name="is_active" value="1" th:checked="${patrolConfig.is_active == 1}">
<textarea name="remark" th:text="${patrolConfig.remark}" class="form-control"></textarea>
```

**修复后**：
```html
<input name="config_id" type="hidden">
<input name="location_name" class="form-control" type="text" required>
<input name="planned_time" class="form-control" type="time" required>
<input type="radio" name="is_active" value="1">
<textarea name="remark" class="form-control"></textarea>
```

### 2. 使用 renderForm 动态加载数据

**修复前**：
```javascript
var configId = [[${patrolConfig.config_id}]];
$(function() {
    initPatrolUsersSelect();
});
```

**修复后**：
```javascript
var configId = $.common.getUrlParam('configId') || window.location.pathname.split('/').pop();
$(function() {
    $('#form-patrol-edit').renderForm({
        url: prefix + "/config/detail/" + configId
    }, function(data) {
        initPatrolUsersSelect();
    });
});
```

## 修复效果

### ✅ 解决的问题
1. **模板解析错误**：消除 Thymeleaf 解析异常
2. **数据加载**：通过 renderForm 正确加载配置数据
3. **字段显示**：所有字段（巡更人员、状态、备注）正常显示
4. **数据绑定**：表单字段正确填充现有数据

### ✅ 保持的功能
1. **select2 组件**：巡更人员选择功能正常
2. **快速时间选择**：时间快选按钮正常工作
3. **表单验证**：字段验证规则保持不变
4. **数据保存**：保存逻辑不受影响

## 测试验证

### 1. 页面加载测试
1. 访问编辑页面：`/oc/patrol/config/edit/{configId}`
2. 检查页面是否正常加载，无模板错误
3. 确认所有字段都正确显示

### 2. 数据显示测试
1. **基本字段**：地点名称、地址、计划时间正确显示
2. **巡更人员**：select2 组件显示已选择的用户
3. **状态字段**：单选按钮选中正确的状态
4. **备注字段**：文本域显示现有备注内容

### 3. 功能测试
1. **编辑保存**：修改数据后能正常保存
2. **用户选择**：可以重新选择巡更人员
3. **时间选择**：快选按钮正常工作
4. **状态切换**：可以切换启用/禁用状态

## 技术要点

### renderForm 方法
- 自动根据字段名称填充表单数据
- 支持嵌套对象和数组数据
- 提供回调函数处理特殊逻辑

### 配置ID 获取
- 从 URL 参数或路径中提取配置ID
- 兼容不同的路由方式

### 数据加载时机
- 在 renderForm 回调中初始化 select2
- 确保数据加载完成后再处理用户选择

这种修复方式与项目整体架构保持一致，避免了 Thymeleaf 模板解析问题。
