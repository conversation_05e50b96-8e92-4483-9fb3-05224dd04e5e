package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.jfinal.model.OcOwnerModel;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业主信息管理 控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/owner")
public class OcOwnerController extends BaseController {
    
    private static final String PREFIX = "oc/user";

    @Autowired
    private IHouseInfoService houseInfoService;

    /**
     * 跳转到业主管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list-owner";
    }

    /**
     * 待审核住户
     * @return
     */
    @GetMapping("/check")
    public String check() {
        return PREFIX + "/check-owner";
    }

    /**
     * 业委会成员管理
     * @return
     */
    @GetMapping("/committee")
    public String committee() {
        return PREFIX + "/committee-owner";
    }

    /**
     * 跳转到新增业主页面
     */
    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add-owner";
    }

    @GetMapping("/importPage")
    public String importPage() {
        return PREFIX + "/import-page";
    }

    /**
     * 跳转到编辑业主页面
     */
    @GetMapping("/edit/{ownerId}")
    public String edit(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());
        return PREFIX + "/edit-owner";
    }

    /**
     * 跳转到业主详情页面
     */
    @GetMapping("/detail/{ownerId}")
    public String detail(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        // 获取业主信息
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());

        // 获取业主关联的房屋列表
        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );
        mmap.put("houses", houses);

        // 获取业主关联的车辆列表
        List<Record> vehicles = Db.find(
            "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.check_status " +
            "FROM eh_vehicle_owner_rel r " +
            "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );
        mmap.put("vehicles", vehicles);

        return PREFIX + "/detail-owner";
    }

    /**
     * 房屋绑定管理页面
     */
    @GetMapping("/houseBindings/{ownerId}")
    public String houseBindings(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        mmap.put("ownerId", ownerId);
        return PREFIX + "/house-bindings";
    }

    /**
     * 查询业主列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);

        // 根据是否有楼栋单元过滤来决定SELECT字段
        String buildingId = params.getString("buildingId");
        String unitId = params.getString("unitId");
        String selectFields;

        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            // 关联查询时需要指定表别名
            selectFields = "select o.*";
        } else {
            // 普通查询
            selectFields = "select *";
        }

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            selectFields,
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/checkList")
    @ResponseBody
    public TableDataInfo checkList() {
        JSONObject params = getParams();
        EasySQL sql = buildCheckListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select r.rel_id,r.check_status,r.house_id, r.owner_id, r.rel_type, r.create_time as apply_time, r.remark as apply_remark, r.approve_info, r.file_id, " +
                "o.owner_name, o.mobile, o.gender, o.id_card, " +
                "h.combina_name, h.room, h.building_name, h.unit_name, h.use_area",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 业委会成员列表查询
     */
    @PostMapping("/committeeList")
    @ResponseBody
    public TableDataInfo committeeList() {
        JSONObject params = getParams();
        EasySQL sql = buildCommitteeListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 新增业委会成员
     */
    @PostMapping("/addCommitteeMember")
    @ResponseBody
    @Log(title = "新增业委会成员", businessType = BusinessType.UPDATE)
    public AjaxResult addCommitteeMember() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 检查业主是否存在
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ? AND community_id = ?",
                ownerId, getSysUser().getCommunityId());

            if (owner == null) {
                return AjaxResult.error("业主不存在或不属于当前小区");
            }

            // 检查是否已经是业委会成员
            if ("2".equals(owner.getStr("role"))) {
                return AjaxResult.error("该业主已经是业委会成员");
            }

            // 更新业主角色为业委会成员
            int updateCount = Db.update("UPDATE eh_owner SET role = 2, update_time = ?, updater = ? WHERE owner_id = ?",
                DateUtils.getTime(), getSysUser().getUserName(), ownerId);

            if (updateCount > 0) {
                return AjaxResult.success("添加业委会成员成功");
            } else {
                return AjaxResult.error("添加业委会成员失败");
            }

        } catch (Exception e) {
            logger.error("添加业委会成员失败", e);
            return AjaxResult.error("添加业委会成员失败：" + e.getMessage());
        }
    }

    /**
     * 移除业委会成员
     */
    @PostMapping("/removeCommitteeMember")
    @ResponseBody
    @Log(title = "移除业委会成员", businessType = BusinessType.UPDATE)
    public AjaxResult removeCommitteeMember() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 检查业主是否存在且是业委会成员
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ? AND community_id = ? AND role = 2",
                ownerId, getSysUser().getCommunityId());

            if (owner == null) {
                return AjaxResult.error("业委会成员不存在或不属于当前小区");
            }

            // 更新业主角色为普通业主
            int updateCount = Db.update("UPDATE eh_owner SET role = 1, update_time = ?, updater = ? WHERE owner_id = ?",
                DateUtils.getTime(), getSysUser().getUserName(), ownerId);

            if (updateCount > 0) {
                return AjaxResult.success("移除业委会成员成功");
            } else {
                return AjaxResult.error("移除业委会成员失败");
            }

        } catch (Exception e) {
            logger.error("移除业委会成员失败", e);
            return AjaxResult.error("移除业委会成员失败：" + e.getMessage());
        }
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String ownerId = params.getString("owner_id");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        Record owner = Db.findFirst("select * from eh_owner where owner_id = ?", ownerId);
        return AjaxResult.success(null, owner.toMap());
    }

    /**
     * 新增业主信息
     */
    @PostMapping("/add")
    @ResponseBody
    @Log(title = "新增业主信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);
        setCreateAndUpdateInfo(model);
        model.save();
        return AjaxResult.success();
    }

    /**
     * 修改业主信息
     */
    @PostMapping("/edit")
    @ResponseBody
    @Log(title = "修改业主信息", businessType = BusinessType.UPDATE)
    public AjaxResult edit() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);
        setUpdateInfo(model);
        boolean result = model.update();
        houseInfoService.updateOwnerHouseInfo(model.getStr("owner_id"));
        return toAjax(result);
    }

    /**
     * 删除业主信息
     */
    @PostMapping("/remove")
    @ResponseBody
    @Log(title = "删除业主信息", businessType = BusinessType.DELETE)
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数ids不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            if(OcOwnerModel.dao.deleteById(id)){
                Db.update("delete from eh_house_owner_rel where owner_id = ?", id);
            }
        }
        return success();
    }

    /**
     * 审批处理（统一方法）
     */
    @PostMapping("/approve")
    @ResponseBody
    @Log(title = "审批处理", businessType = BusinessType.UPDATE)
    public AjaxResult approve() {
        JSONObject params = getParams();
        String relIds = params.getString("relIds");
        String approveType = params.getString("approveType"); // "approve" 或 "reject"
        String rejectReason = params.getString("rejectReason");

        if (StringUtils.isEmpty(relIds)) {
            return AjaxResult.error("请选择要审批的记录");
        }

        if ("reject".equals(approveType) && StringUtils.isEmpty(rejectReason)) {
            return AjaxResult.error("审批不通过时必须填写原因");
        }

        String[] relIdArr = relIds.split(",");
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String approver = getSysUser().getLoginName();
        boolean isApprove = "approve".equals(approveType);
        int checkStatus = isApprove ? 1 : 2;

        try {
            // 构建审批信息JSON
            JSONObject approveInfo = new JSONObject();
            approveInfo.put("approve_time", now);
            approveInfo.put("approve_by", approver);
            approveInfo.put("approve_type", relIdArr.length > 1 ? "batch" : "single");
            approveInfo.put("status", isApprove ? "approved" : "rejected");
            if (!isApprove) {
                approveInfo.put("reject_reason", rejectReason);
            }

            for (String relId : relIdArr) {
                // 更新审核状态和审批信息
                Db.update("UPDATE eh_house_owner_rel SET check_status = ?, update_time = ?, update_by = ?, approve_info = ? WHERE rel_id = ?",
                    checkStatus, now, approver, approveInfo.toJSONString(), relId);

                // 如果是审批通过，需要更新相关统计
                if (isApprove) {
                    Record rel = Db.findFirst("SELECT house_id, owner_id FROM eh_house_owner_rel WHERE rel_id = ?", relId);
                    if (rel != null) {
                        houseInfoService.updateHouseOwnerInfo(rel.getStr("house_id"));
                        houseInfoService.updateOwnerHouseInfo(rel.getStr("owner_id"));
                    }
                }
            }

            String message = isApprove ? "审批通过成功" : "审批不通过成功";
            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("审批处理失败", e);
            return AjaxResult.error("审批处理失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取业主关联的房屋列表
     */
    @PostMapping("/houseList")
    @ResponseBody
    public TableDataInfo houseList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }
        
        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC", 
            ownerId
        );
        
        return getDataList(houses);
    }
    
    /**
     * 获取业主关联的车辆列表
     */
    @PostMapping("/vehicleList")
    @ResponseBody
    public TableDataInfo vehicleList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }

        List<Record> vehicles = Db.find(
            "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.vehicle_type, v.owner_real_name, v.parking_space, v.check_status " +
            "FROM eh_vehicle_owner_rel r " +
            "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );

        return getDataList(vehicles);
    }

    /**
     * 获取业主关联的车位列表
     */
    @PostMapping("/parkingList")
    @ResponseBody
    public TableDataInfo parkingList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }

        List<Record> parkings = Db.find(
            "SELECT r.*, p.parking_no, p.parking_type, p.parking_status, p.parking_area, p.plate_no " +
            "FROM eh_parking_owner_rel r " +
            "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC",
            ownerId
        );

        return getDataList(parkings);
    }

    /**
     * 获取房屋绑定数据
     */
    @PostMapping("/getHouseBindingData")
    @ResponseBody
    public AjaxResult getHouseBindingData() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        try {
            // 获取业主基本信息
            Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ?", ownerId);
            if (owner == null) {
                return AjaxResult.error("业主不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("ownerName", owner.getStr("owner_name"));
            result.put("ownerMobile", owner.getStr("mobile"));
            result.put("houseInfo", owner.getStr("house_info"));

            // 获取绑定的房屋列表
            List<Record> houses = Db.find(
                "SELECT r.rel_id, r.house_id, r.rel_type, r.is_default, r.remark, h.combina_name, h.room, h.use_area " +
                "FROM eh_house_owner_rel r " +
                "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
                "WHERE r.owner_id = ? and r.check_status = 1 " +
                "ORDER BY r.is_default DESC, r.create_time DESC",
                ownerId
            );
            result.put("houses", recordToMap(houses));

            // 获取绑定的车位列表
            List<Record> parkings = Db.find(
                "SELECT r.rel_id, r.parking_id, r.remark, p.parking_no, p.parking_type " +
                "FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                "WHERE r.owner_id = ? " +
                "ORDER BY r.create_time DESC",
                ownerId
            );
            result.put("parkings", recordToMap(parkings));

            // 获取绑定的车辆列表
            List<Record> vehicles = Db.find(
                "SELECT r.rel_id, r.vehicle_id, r.remark, v.plate_no, v.vehicle_brand, v.vehicle_model " +
                "FROM eh_vehicle_owner_rel r " +
                "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                "WHERE r.owner_id = ? " +
                "ORDER BY r.create_time DESC",
                ownerId
            );
            result.put("vehicles", recordToMap(vehicles));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋绑定数据失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();

        // 检查是否需要按楼栋单元过滤
        String buildingId = params.getString("buildingId");
        String unitId = params.getString("unitId");

        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            // 需要关联查询房屋信息来过滤业主
            sql.append("from eh_owner o");
            sql.append("inner join eh_house_owner_rel r on o.owner_id = r.owner_id");
            sql.append("inner join eh_house_info h on r.house_id = h.house_id");
            sql.append("where r.check_status = 1");

            sql.append(getSysUser().getCommunityId(),"and o.community_id = ?");

            // 楼栋过滤
            sql.append(buildingId, "and h.building_id = ?");
            // 单元过滤
            sql.append(unitId, "and h.unit_id = ?");
        } else {
            // 普通查询，不需要关联
            sql.append("from eh_owner o where 1=1");
            sql.append(getSysUser().getCommunityId(),"and o.community_id = ?");
        }

        // 业主姓名 - 模糊查询
        sql.appendLike(params.getString("owner_name"), "and o.owner_name like ?");
        // 手机号码 - 精确匹配
        sql.appendLike(params.getString("mobile"), "and o.mobile like ?");
        // 身份证号码 - 精确匹配
        sql.append(params.getString("id_card"), "and o.id_card = ?");
        // 性别 - 精确匹配
        sql.append(params.getString("gender"), "and o.gender = ?");
        // 入住状态过滤
        sql.append(params.getString("is_live"), "and o.is_live = ?");

        // 创建时间范围查询
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and o.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and o.create_time <= ?");

        // 如果是关联查询，需要去重并按业主分组
        if (StringUtils.isNotEmpty(buildingId) || StringUtils.isNotEmpty(unitId)) {
            sql.append("group by o.owner_id");
        }

        // 默认按创建时间倒序
        sql.append("order by o.create_time desc");
        return sql;
    }

    /**
     * 构建业委会成员列表查询SQL
     */
    private EasySQL buildCommitteeListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_owner where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.append("and role = 2"); // 业委会成员

        // 搜索条件
        sql.appendLike(params.getString("owner_name"), "and owner_name like ?");
        sql.appendLike(params.getString("mobile"), "and mobile like ?");
        sql.appendLike(params.getString("house_info"), "and house_info like ?");

        // 默认按创建时间倒序
        sql.append("order by create_time desc");
        return sql;
    }

    /**
     * 构建待审核列表查询SQL
     */
    private EasySQL buildCheckListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_house_owner_rel r");
        sql.append("left join eh_owner o on r.owner_id = o.owner_id");
        sql.append("left join eh_house_info h on r.house_id = h.house_id");
        sql.append("where r.apply_flag = 1");
        sql.append(getSysUser().getCommunityId(), "and r.community_id = ?");

        sql.append(params.getString("checkStatus"),"and r.check_status = ?");

        // 业主姓名 - 模糊查询
        sql.appendLike(params.getString("owner_name"), "and o.owner_name like ?");
        // 手机号码 - 模糊查询
        sql.appendLike(params.getString("mobile"), "and o.mobile like ?");
        // 房屋信息 - 模糊查询
        String houseInfo = params.getString("house_info");
        if (StringUtils.isNotEmpty(houseInfo)) {
            sql.append("and (h.combina_name like '%" + houseInfo + "%' or h.room like '%" + houseInfo + "%')");
        }
        // 关系类型 - 精确匹配
        sql.append(params.getString("rel_type"), "and r.rel_type = ?");

        // 申请时间范围查询
        String beginTime = params.getString("beginTime");
        if (StringUtils.isNotEmpty(beginTime)) {
            sql.append(beginTime+" 00:00:00", "and r.create_time >= ?");
        }
        String endTime = params.getString("endTime");
        if (StringUtils.isNotEmpty(endTime)) {
            sql.append(endTime+" 23:59:59", "and r.create_time <= ?");
        }
        // 默认按申请时间倒序
        sql.append("order by r.create_time desc");
        return sql;
    }

    /**
     * 设置创建和更新信息
     */
    private void setCreateAndUpdateInfo(OcOwnerModel model) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        model.set("create_time", now);
        model.set("update_time", now);
        model.set("creator", loginName);
        model.set("updater", loginName);
        model.set("pms_id", getSysUser().getPmsId());
        model.set("community_id", getSysUser().getCommunityId());

    }

    /**
     * 获取可选房屋列表
     */
    @PostMapping("/getAvailableHouses")
    @ResponseBody
    public AjaxResult getAvailableHouses() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_house_info WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的房屋

            // 排除已绑定给该业主的房屋
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND house_id NOT IN (SELECT house_id FROM eh_house_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND (combina_name LIKE '%" + search + "%' OR room LIKE '%" + search + "%')");
            }

            sql.append("ORDER BY combina_name, room");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT house_id as id, CONCAT(combina_name, '/', room, ' (', use_area, '㎡)') as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选房屋列表失败", e);
            return AjaxResult.error("获取房屋列表失败");
        }
    }

    /**
     * 获取可选车位列表
     */
    @PostMapping("/getAvailableParkings")
    @ResponseBody
    public AjaxResult getAvailableParkings() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_parking_space WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的车位

            // 排除已绑定给该业主的车位
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND parking_id NOT IN (SELECT parking_id FROM eh_parking_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND parking_no LIKE '%" + search + "%'");
            }

            sql.append("ORDER BY parking_no");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT parking_id as id, CONCAT(parking_no, ' (', CASE parking_type WHEN 1 THEN '私人车位' WHEN 2 THEN '子母车位' ELSE '未知' END, ')') as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选车位列表失败", e);
            return AjaxResult.error("获取车位列表失败");
        }
    }

    /**
     * 获取可选车辆列表
     */
    @PostMapping("/getAvailableVehicles")
    @ResponseBody
    public AjaxResult getAvailableVehicles() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String ownerId = params.getString("ownerId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20; // 每页20条数据

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_vehicle WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
            sql.append("AND check_status = 1"); // 只显示已审核的车辆

            // 排除已绑定给该业主的车辆
            if (StringUtils.isNotEmpty(ownerId)) {
                sql.append("AND vehicle_id NOT IN (SELECT vehicle_id FROM eh_vehicle_owner_rel WHERE owner_id = '" + ownerId + "')");
            }

            // 搜索条件 - 如果有搜索词则添加搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND (plate_no LIKE '%" + search + "%' OR vehicle_brand LIKE '%" + search + "%')");
            }

            sql.append("ORDER BY plate_no");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT vehicle_id as id, CONCAT(plate_no, CASE WHEN vehicle_brand IS NOT NULL AND vehicle_brand != '' THEN CONCAT(' (', vehicle_brand, CASE WHEN vehicle_model IS NOT NULL AND vehicle_model != '' THEN CONCAT(' ', vehicle_model) ELSE '' END, ')') ELSE '' END) as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选车辆列表失败", e);
            return AjaxResult.error("获取车辆列表失败");
        }
    }

    /**
     * 添加房屋绑定
     */
    @PostMapping("/addHouseBinding")
    @ResponseBody
    @Log(title = "绑定房屋", businessType = BusinessType.INSERT)
    public AjaxResult addHouseBinding() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String houseId = params.getString("houseId");
        Integer relType = params.getInteger("relType");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (relType == null) {
            relType = 1; // 默认为业主
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                houseId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("owner_id", ownerId);
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("rel_type", relType);
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
            if (success) {
                // 更新房屋的绑定业主数量
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息
                updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定房屋失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 移除房屋绑定
     */
    @PostMapping("/removeHouseBinding")
    @ResponseBody
    @Log(title = "解绑房屋", businessType = BusinessType.DELETE)
    public AjaxResult removeHouseBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_house_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String houseId = rel.getStr("house_id");
            String ownerId = rel.getStr("owner_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);
            if (success) {
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息
                updateOwnerHouseInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑房屋失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 添加车位绑定
     */
    @PostMapping("/addParkingBinding")
    @ResponseBody
    @Log(title = "绑定车位", businessType = BusinessType.INSERT)
    public AjaxResult addParkingBinding() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_parking_owner_rel WHERE parking_id = ? AND owner_id = ?",
                parkingId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此车位");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("parking_id", parkingId);
            rel.set("owner_id", ownerId);
            rel.set("rel_type", 1); // 默认为业主
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_parking_owner_rel", "rel_id", rel);
            if (success) {
                // 更新车位的绑定业主数量和业主名称
                updateParkingOwnerInfo(parkingId);

                // 更新业主的车位统计信息
                updateOwnerParkingInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车位失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 移除车位绑定
     */
    @PostMapping("/removeParkingBinding")
    @ResponseBody
    @Log(title = "解绑车位", businessType = BusinessType.DELETE)
    public AjaxResult removeParkingBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_parking_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String parkingId = rel.getStr("parking_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_parking_owner_rel", "rel_id", relId);
            if (success) {
                // 更新车位的绑定业主数量和业主名称
                updateParkingOwnerInfo(parkingId);

                // 更新业主的车位统计信息
                updateOwnerParkingInfo(rel.getStr("owner_id"));
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 添加车辆绑定
     */
    @PostMapping("/addVehicleBinding")
    @ResponseBody
    @Log(title = "绑定车辆", businessType = BusinessType.INSERT)
    public AjaxResult addVehicleBinding() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        String vehicleId = params.getString("vehicleId");

        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_owner_rel WHERE vehicle_id = ? AND owner_id = ?",
                vehicleId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此车辆");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("vehicle_id", vehicleId);
            rel.set("owner_id", ownerId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_vehicle_owner_rel", "rel_id", rel);
            if (success) {
                // 更新车辆的绑定业主数量
                Db.update(
                    "UPDATE eh_vehicle SET owner_count = (SELECT COUNT(*) FROM eh_vehicle_owner_rel WHERE vehicle_id = ?) WHERE vehicle_id = ?",
                    vehicleId, vehicleId
                );

                // 更新业主的车辆统计信息
                updateOwnerVehicleInfo(ownerId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车辆失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 查询业主绑定的房屋列表
     */
    @RequestMapping("/houseBindings")
    @ResponseBody
    public AjaxResult houseBindings() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        String sql = "SELECT r.*, h.combina_name, h.room, h.use_area, h.house_status FROM eh_house_owner_rel r " +
                     "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
                     "WHERE r.owner_id = ? and r.check_status = 1 ORDER BY r.is_default DESC, r.create_time DESC";
        List<Record> list = Db.find(sql, ownerId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 查询业主绑定的车辆列表
     */
    @RequestMapping("/vehicleBindings")
    @ResponseBody
    public AjaxResult vehicleBindings() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        String sql = "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.vehicle_type FROM eh_vehicle_owner_rel r " +
                     "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                     "WHERE r.owner_id = ? ORDER BY r.is_default DESC, r.create_time DESC";
        List<Record> list = Db.find(sql, ownerId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 查询业主绑定的车位列表
     */
    @RequestMapping("/parkingBindings")
    @ResponseBody
    public AjaxResult parkingBindings() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }

        String sql = "SELECT r.*, p.parking_no, p.parking_type, p.parking_status FROM eh_parking_owner_rel r " +
                     "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                     "WHERE r.owner_id = ? ORDER BY r.create_time DESC";
        List<Record> list = Db.find(sql, ownerId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 移除车辆绑定
     */
    @PostMapping("/removeVehicleBinding")
    @ResponseBody
    @Log(title = "解绑车辆", businessType = BusinessType.DELETE)
    public AjaxResult removeVehicleBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取绑定关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_vehicle_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String vehicleId = rel.getStr("vehicle_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_vehicle_owner_rel", "rel_id", relId);
            if (success) {
                // 更新车辆的绑定业主数量
                Db.update("UPDATE eh_vehicle SET owner_count = (SELECT COUNT(*) FROM eh_vehicle_owner_rel WHERE vehicle_id = ?) WHERE vehicle_id = ?",
                    vehicleId, vehicleId
                );

                // 更新业主的车辆统计信息
                updateOwnerVehicleInfo(rel.getStr("owner_id"));
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车辆失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 更新业主的房屋信息
     */
    private void updateOwnerHouseInfo(String ownerId) {
        houseInfoService.updateOwnerHouseInfo(ownerId);
    }

    /**
     * 更新车位表中的业主信息
     */
    private void updateParkingOwnerInfo(String parkingId) {
        try {
            // 获取车位绑定的业主数量
            int ownerCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_parking_owner_rel WHERE parking_id = ?",
                parkingId
            );

            String ownerName = "";
            Record owner = Db.findFirst(
                "SELECT GROUP_CONCAT(o.owner_name) as names FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id WHERE r.parking_id = ?",
                parkingId
            );
            if (owner != null && owner.getStr("names") != null) {
                ownerName = owner.getStr("names");
            }

            // 更新车位表中的业主信息
            Db.update(
                "UPDATE eh_parking_space SET owner_count = ?, owner_name = ? WHERE parking_id = ?",
                ownerCount, ownerName, parkingId
            );

            logger.info("更新车位业主信息成功，车位ID: {}, 业主数量: {}, 业主名称: {}", parkingId, ownerCount, ownerName);
        } catch (Exception e) {
            logger.error("更新车位业主信息失败", e);
        }
    }

    /**
     * 更新业主的车位信息
     */
    private void updateOwnerParkingInfo(String ownerId) {
        try {
            // 获取业主绑定的车位数量
            int parkingCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_parking_owner_rel WHERE owner_id = ?",
                ownerId
            );

            // 获取业主绑定的车位号（使用GROUP_CONCAT拼接）
            String parkingNo = "";
            Record parking = Db.findFirst(
                "SELECT GROUP_CONCAT(p.parking_no ORDER BY r.create_time ASC SEPARATOR ',') as parking_nos " +
                "FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                "WHERE r.owner_id = ?",
                ownerId
            );
            if (parking != null && parking.getStr("parking_nos") != null) {
                parkingNo = parking.getStr("parking_nos");
            }

            // 更新业主表的车位数量和车位号
            Db.update(
                "UPDATE eh_owner SET parking_count = ?, parking_no = ? WHERE owner_id = ?",
                parkingCount, parkingNo, ownerId
            );

            logger.info("更新业主车位信息成功，业主ID: {}, 车位数量: {}, 车位号: {}", ownerId, parkingCount, parkingNo);
        } catch (Exception e) {
            logger.error("更新业主车位信息失败", e);
        }
    }

    /**
     * 更新业主的车辆信息
     */
    private void updateOwnerVehicleInfo(String ownerId) {
        try {
            Record r = Db.findFirst(
                    "SELECT GROUP_CONCAT(v.plate_no) AS car_info, COUNT(*) AS car_count " +
                            "FROM eh_vehicle_owner_rel r LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id WHERE r.owner_id = ?",
                    ownerId
            );

            if (r != null) {
                String carInfo = r.getStr("car_info");
                int carCount = r.getInt("car_count");

                Db.update("UPDATE eh_owner SET car_count = ?, car_info = ? WHERE owner_id = ?", carCount, carInfo, ownerId);
                logger.info("更新业主车辆信息成功，业主ID: {}, 车辆数量: {}", ownerId, carCount);
            }
        } catch (Exception e) {
            logger.error("更新业主车辆信息失败", e);
        }

    }

    /**
     * 设置更新信息
     */
    private void setUpdateInfo(OcOwnerModel model) {
        model.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        model.set("updater", getSysUser().getLoginName());
    }
}
