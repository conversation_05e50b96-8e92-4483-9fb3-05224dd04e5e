<view class="dashboard-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <view wx:else>
    <!-- 统计数据区域 -->
    <view class="stats-section">
      <text class="section-title">数据概览</text>
      <view class="stats-grid">
        <view class="stat-item urgent">
          <text class="stat-number">{{stats.pendingRepair}}</text>
          <text class="stat-label">待处理报修</text>
        </view>
        <view class="stat-item processing">
          <text class="stat-number">{{stats.processingRepair}}</text>
          <text class="stat-label">处理中报修</text>
        </view>
        <view class="stat-item urgent">
          <text class="stat-number">{{stats.pendingComplaint}}</text>
          <text class="stat-label">待处理投诉</text>
        </view>
        <view class="stat-item processing">
          <text class="stat-number">{{stats.processingComplaint}}</text>
          <text class="stat-label">处理中投诉</text>
        </view>
        <view class="stat-item today">
          <text class="stat-number">{{stats.todayRepair}}</text>
          <text class="stat-label">今日新增报修</text>
        </view>
        <view class="stat-item today">
          <text class="stat-number">{{stats.todayComplaint}}</text>
          <text class="stat-label">今日新增投诉</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作区域 -->
    <view class="quick-actions">
      <text class="section-title">快捷操作</text>
      <view class="action-grid">
        <view class="action-item" bindtap="goToOrders" data-type="repair">
          <van-icon name="tool" size="24px" color="#1989fa" />
          <text class="action-text">报修管理</text>
        </view>
        <view class="action-item" bindtap="goToOrders" data-type="complaint">
          <van-icon name="chat-o" size="24px" color="#ff976a" />
          <text class="action-text">投诉管理</text>
        </view>
        <view class="action-item" bindtap="goToPatrol">
          <van-icon name="location-o" size="24px" color="#07c160" />
          <text class="action-text">巡检记录</text>
        </view>
        <view class="action-item" bindtap="goToOrders">
          <van-icon name="orders-o" size="24px" color="#ed6a0c" />
          <text class="action-text">全部工单</text>
        </view>
      </view>
    </view>

    <!-- 最近报修 -->
    <view wx:if="{{recentItems.repairs.length > 0}}" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近报修</text>
        <text class="more-link" bindtap="goToOrders" data-type="repair">查看更多</text>
      </view>
      <view class="recent-list">
        <view wx:for="{{recentItems.repairs}}" wx:key="id" class="recent-item" bindtap="viewRepairDetail" data-id="{{item.id}}">
          <view class="item-content">
            <view class="item-title">{{item.type || '报修'}}</view>
            <view class="item-desc">{{item.contentSummary}}</view>
            <view class="item-info">
              <text class="item-time">{{item.create_time}}</text>
              <text class="item-user">{{item.name}}</text>
            </view>
          </view>
          <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
        </view>
      </view>
    </view>

    <!-- 最近投诉 -->
    <view wx:if="{{recentItems.complaints.length > 0}}" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近投诉建议</text>
        <text class="more-link" bindtap="goToOrders" data-type="complaint">查看更多</text>
      </view>
      <view class="recent-list">
        <view wx:for="{{recentItems.complaints}}" wx:key="id" class="recent-item" bindtap="viewComplaintDetail" data-id="{{item.id}}">
          <view class="item-content">
            <view class="item-title">{{item.type || '投诉建议'}}</view>
            <view class="item-desc">{{item.contentSummary}}</view>
            <view class="item-info">
              <text class="item-time">{{item.create_time}}</text>
              <text class="item-user">{{item.name}}</text>
            </view>
          </view>
          <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
