# TabBar重构指南

## 概述

按照微信官方最佳实践重构了TabBar系统，解决了物业用户登录后显示业主菜单的问题。

## 重构内容

### 1. 清空登录服务中的TabBar处理

`loginService.js` 中的 `initTabBarConfig` 方法已清空，TabBar现在由页面自行管理。

### 2. 重写TabBar组件

`custom-tab-bar/index.js` 按照微信官方最佳实践重写：

- 使用 `active` 字段替代 `selected`
- 简化配置结构，移除不必要的字段
- 实现 `toggleMenu(userType)` 方法切换菜单
- 实现 `init()` 方法设置选中状态
- 修复 `onChange` 事件处理，正确获取索引值

### 3. 重写页面工具函数

`pageUtils.js` 简化为两个核心函数：

- `handlePropertyPageShow(pageInstance, loadDataFn)` - 物业页面处理
- `handleOwnerPageShow(pageInstance, loadDataFn)` - 业主页面处理

### 4. 更新所有TabBar页面

所有TabBar页面的 `onShow` 方法都已更新，调用对应的工具函数。

## 使用方法

### 物业页面

```javascript
import { handlePropertyPageShow } from '../../utils/pageUtils.js'

Page({
  onShow() {
    handlePropertyPageShow(this, this.loadData)
  },
  
  loadData() {
    // 页面数据加载逻辑
  }
})
```

### 业主页面

```javascript
onShow() {
  // 设置tabBar（使用公共工具函数）
  const { initTabBar } = require('../../utils/pageUtils.js')
  initTabBar('1')

  // 或者使用自动检测用户类型的函数
  // const { autoInitTabBar } = require('../../utils/pageUtils.js')
  // autoInitTabBar()

  // 其他页面逻辑...
}
```

## TabBar配置

### 业主菜单 (userType='1') - 支持动态配置

**必需项（始终显示）：**
- 首页 `/pages/index/index`
- 信息公开 `/pages/service/index`
- 我的 `/pages/mine/index`

**可选项（根据配置显示）：**
- 工作照片 `/pages/markicam/index` - 需要 `enable_markicam` 配置
- 收支公示 `/pages/finance/finance` - 需要 `enable_financial` 配置

**显示规则：**
- 配置全关闭：首页、信息公开、我的（3项）
- 只开启工作照片：首页、信息公开、工作照片、我的（4项）
- 只开启收支公示：首页、信息公开、收支公示、我的（4项）
- 配置全开启：首页、信息公开、工作照片、收支公示、我的（5项）

### 物业菜单 (userType='2')

- 工作台 `/pages/property/dashboard/index`
- 工单 `/pages/property/orders/index`
- 巡检 `/pages/property/patrol/index`
- 我的 `/pages/property/profile/index`

## 核心方法

### TabBar组件方法

- `toggleMenu(userType)` - 根据用户类型切换菜单（支持动态配置）
- `init()` - 初始化选中状态（支持动态索引计算）
- `onChange(event)` - 处理TabBar切换事件
- `filterMenuByConfig(userType)` - 根据配置过滤菜单项

### 页面工具方法

- `initTabBar(userType)` - 通用TabBar初始化函数
- `autoInitTabBar()` - 自动检测用户类型并初始化TabBar
- `handlePropertyPageShow(pageInstance, loadDataFn)` - 物业页面显示处理
- `handleOwnerPageShow(pageInstance, loadDataFn)` - 业主页面显示处理
- `getCurrentUserType()` - 获取当前用户类型
- `isPropertyUser()` - 检查是否为物业用户
- `isOwnerUser()` - 检查是否为业主用户

## 工作流程

1. 用户登录成功后，状态管理器保存用户类型
2. 用户进入TabBar页面时，页面的 `onShow` 方法被调用
3. 页面调用对应的工具函数（物业或业主）
4. 工具函数调用 `tabBar.toggleMenu(userType)` 切换菜单
5. TabBar组件根据配置过滤菜单项（业主用户）
6. 工具函数调用 `tabBar.init()` 设置正确的选中状态
7. TabBar显示对应用户类型的动态菜单

## 测试

运行测试文件验证重构效果：

```javascript
// TabBar重构测试
const { runTabBarRestructureTests } = require('./test/loginService-tabbar-fix-test.js')
runTabBarRestructureTests()

// TabBar点击事件测试
const { runTabBarClickTests } = require('./test/tabbar-click-test.js')
runTabBarClickTests()

// 动态TabBar测试
const { runDynamicTabBarTests } = require('./test/dynamic-tabbar-test.js')
runDynamicTabBarTests()
```

## 注意事项

1. 所有TabBar页面都必须在 `onShow` 中调用对应的处理函数
2. 物业页面使用 `handlePropertyPageShow`
3. 业主页面直接调用 `toggleMenu('1')` 和 `init()`
4. 不再需要在登录服务中处理TabBar配置
5. TabBar配置完全由页面自行管理，符合微信官方最佳实践
