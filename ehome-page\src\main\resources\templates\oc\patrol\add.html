<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增巡更配置')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .user-select-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            min-height: 100px;
            background: #f9f9f9;
        }
        .selected-user {
            display: inline-block;
            background: #5cb85c;
            color: white;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 12px;
        }
        .selected-user .remove-user {
            margin-left: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .map-container {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-patrol-add">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">地点名称：</label>
                <div class="col-sm-8">
                    <input name="location_name" class="form-control" type="text" placeholder="请输入巡更地点名称" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">地点地址：</label>
                <div class="col-sm-8">
                    <input name="location_address" class="form-control" type="text" placeholder="请输入详细地址">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">计划时间：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="planned_time" class="form-control" type="time" required>
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">
                                快选 <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li><a href="javascript:void(0)" onclick="setQuickTime('09:00')">09:00 (早班)</a></li>
                                <li><a href="javascript:void(0)" onclick="setQuickTime('12:00')">12:00 (午班)</a></li>
                                <li><a href="javascript:void(0)" onclick="setQuickTime('14:00')">14:00 (下午)</a></li>
                                <li><a href="javascript:void(0)" onclick="setQuickTime('18:00')">18:00 (晚班)</a></li>
                                <li><a href="javascript:void(0)" onclick="setQuickTime('22:00')">22:00 (夜班)</a></li>
                            </ul>
                        </div>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可使用快选按钮选择常用时间</span>
                </div>
            </div>
            <div class="form-group" style="display: none;">
                <label class="col-sm-3 control-label">允许范围：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="location_range" class="form-control" type="number" value="100" min="10" max="1000">
                        <span class="input-group-addon">米</span>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 巡更时允许的位置偏差范围</span>
                </div>
            </div>
            <div class="form-group" style="display: none;">
                <label class="col-sm-3 control-label">地理位置：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-6">
                            <input name="longitude" class="form-control" type="number" step="0.000001" placeholder="经度">
                        </div>
                        <div class="col-sm-6">
                            <input name="latitude" class="form-control" type="number" step="0.000001" placeholder="纬度">
                        </div>
                    </div>
                    <div class="mt-2">
                        <button type="button" class="btn btn-info btn-sm" onclick="getCurrentLocation()">
                            <i class="fa fa-map-marker"></i> 获取当前位置
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="selectFromMap()">
                            <i class="fa fa-map"></i> 地图选择
                        </button>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选，用于验证巡更位置的准确性</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">巡更人员：</label>
                <div class="col-sm-8">
                    <select id="patrolUsers" name="patrol_users_select" class="form-control" multiple="multiple" style="width: 100%;">
                    </select>
                    <input type="hidden" name="users" id="usersData">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选择多个巡更人员</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="active1" name="is_active" value="1" checked>
                        <label for="active1">启用</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="active0" name="is_active" value="0">
                        <label for="active0">禁用</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注说明：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3" placeholder="请输入备注说明"></textarea>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 地图选择模态框 -->
    <div class="modal fade" id="mapModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">选择地理位置</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="mapContainer" class="map-container"></div>
                    <div class="mt-2">
                        <p>点击地图选择位置，当前选择：</p>
                        <p id="selectedLocation">未选择</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="confirmLocation()">确认选择</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/patrol";
        var selectedUsersData = [];
        var selectedLng = null;
        var selectedLat = null;

        $("#form-patrol-add").validate({
            focusCleanup: true
        });

        // 初始化 select2
        $(function() {
            initPatrolUsersSelect();
        });

        function submitHandler() {
            // 从 select2 获取选中的用户数据
            var selectedUsers = $('#patrolUsers').select2('data');
            if (selectedUsers.length === 0) {
                $.modal.msgError("请至少选择一个巡更人员");
                return false;
            }

            // 转换为后端需要的格式
            var usersData = selectedUsers.map(function(user) {
                return {
                    userId: user.id,
                    userName: user.text,
                    userPhone: user.phone || ''
                };
            });

            $("#usersData").val(JSON.stringify(usersData));

            if ($.validate.form()) {
                $.operate.save(prefix + "/config/save", $('#form-patrol-add').serialize());
            }
        }

        // 初始化巡更人员 select2
        function initPatrolUsersSelect() {
            $('#patrolUsers').select2({
                placeholder: '请选择巡更人员',
                allowClear: true,
                ajax: {
                    url: ctx + 'system/user/list',
                    type: 'POST',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            pageNum: 1,
                            pageSize: 50,
                            userName: params.term || ''
                        };
                    },
                    processResults: function (data) {
                        var results = [];
                        if (data.code === 0 && data.rows) {
                            results = data.rows.map(function(user) {
                                return {
                                    id: user.userId,
                                    text: user.userName,
                                    phone: user.phonenumber || ''
                                };
                            });
                        }
                        return {
                            results: results
                        };
                    },
                    cache: true
                },
                minimumInputLength: 0
            });
        }

        // 设置快速时间
        function setQuickTime(time) {
            $('input[name="planned_time"]').val(time);
        }

        // 获取当前位置
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    $("input[name='longitude']").val(position.coords.longitude.toFixed(6));
                    $("input[name='latitude']").val(position.coords.latitude.toFixed(6));
                    $.modal.msgSuccess("位置获取成功");
                }, function(error) {
                    $.modal.msgError("位置获取失败：" + error.message);
                });
            } else {
                $.modal.msgError("浏览器不支持地理位置获取");
            }
        }

        // 地图选择位置
        function selectFromMap() {
            $("#mapModal").modal('show');
            // 这里可以集成地图API，如高德地图、百度地图等
            // 由于需要API密钥，这里只提供框架
            initMap();
        }

        // 初始化地图
        function initMap() {
            // 地图初始化代码
            // 可以使用高德地图、百度地图等
            $("#mapContainer").html('<div class="text-center" style="padding: 100px 0;"><p>地图功能需要配置地图API</p><p>请联系管理员配置地图服务</p></div>');
        }

        // 确认位置选择
        function confirmLocation() {
            if (selectedLng && selectedLat) {
                $("input[name='longitude']").val(selectedLng);
                $("input[name='latitude']").val(selectedLat);
                $("#mapModal").modal('hide');
                $.modal.msgSuccess("位置选择成功");
            } else {
                $.modal.msgError("请先在地图上选择位置");
            }
        }
    </script>
</body>
</html>
