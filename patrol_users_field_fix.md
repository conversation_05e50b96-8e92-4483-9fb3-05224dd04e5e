# 巡更配置 patrol_users 字段修复

## 问题描述
用户反馈新增配置时 patrol_users 字段没有数据。

## 问题分析
1. **前端问题**：select2 组件配置返回包含电话号码的文本，导致用户名不纯净
2. **后端问题**：使用 `params.getJSONArray("users")` 直接解析，但前端传递的是 JSON 字符串，需要先解析

## 修复内容

### 1. 前端修复
**文件**：`ehome-page/src/main/resources/templates/oc/patrol/add.html` 和 `edit.html`

```javascript
// 修复前 - select2 配置
results = data.rows.map(function(user) {
    return {
        id: user.userId,
        text: user.userName + (user.phonenumber ? ' (' + user.phonenumber + ')' : ''),  // 包含电话号码
        phone: user.phonenumber || ''
    };
});

// 修复后 - select2 配置
results = data.rows.map(function(user) {
    return {
        id: user.userId,
        text: user.userName,  // 只返回纯用户名
        phone: user.phonenumber || ''
    };
});
```

### 2. 后端修复
**文件**：`ehome-oc/src/main/java/com/ehome/oc/controller/assets/PatrolController.java`

```java
// 修复前
JSONArray users = params.getJSONArray("users");

// 修复后
String usersStr = params.getString("users");
JSONArray users;
try {
    users = JSONArray.parseArray(usersStr);
} catch (Exception e) {
    logger.error("解析用户数据失败: " + usersStr, e);
    return AjaxResult.error("用户数据格式错误");
}
```

## 测试验证

### 1. 新增配置测试
1. 访问 `/oc/patrol/config`
2. 点击"新增配置"
3. 填写地点名称和计划时间
4. 使用 select2 选择巡更人员
5. 保存配置
6. 检查数据库：
   ```sql
   SELECT config_id, location_name, patrol_users 
   FROM eh_patrol_config 
   ORDER BY create_time DESC LIMIT 1;
   ```

### 2. 编辑配置测试
1. 编辑现有配置
2. 修改巡更人员
3. 保存后检查 patrol_users 字段是否正确更新

## 预期结果
- `patrol_users` 字段正确保存用户姓名（逗号分隔）
- 配置列表正确显示巡更人员姓名
- 编辑时正确加载已选择的用户

## 相关文件
- `ehome-page/src/main/resources/templates/oc/patrol/add.html`
- `ehome-page/src/main/resources/templates/oc/patrol/edit.html`
- `ehome-oc/src/main/java/com/ehome/oc/controller/assets/PatrolController.java`
