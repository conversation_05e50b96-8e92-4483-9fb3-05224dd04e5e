package com.ehome.oc.service;

import com.ehome.common.core.domain.model.LoginUser;
import java.util.Map;

/**
 * 用户状态服务接口
 * 统一处理用户状态相关的业务逻辑，避免代码重复
 */
public interface UserStatusService {
    
    /**
     * 构建用户状态数据
     * @param currentUser 当前登录用户
     * @param includeSubscribeStatus 是否包含订阅状态
     * @return 用户状态数据
     */
    Map<String, Object> buildUserStatusData(LoginUser currentUser, boolean includeSubscribeStatus);
    
    /**
     * 构建用户状态数据（完整版本）
     * @param currentUser 当前登录用户
     * @return 用户状态数据
     */
    Map<String, Object> buildUserStatusData(LoginUser currentUser);
    
    /**
     * 清除用户相关缓存
     * @param ownerId 业主ID
     * @param communityId 社区ID
     * @param userId 用户ID
     */
    void clearUserCache(String ownerId, String communityId, String userId);
    
    /**
     * 获取房屋信息（带缓存）
     * @param ownerId 业主ID
     * @return 房屋信息
     */
    Map<String, Object> getHouseInfoWithCache(String ownerId);
    
    /**
     * 获取社区信息（带缓存）
     * @param communityId 社区ID
     * @return 社区信息
     */
    Map<String, Object> getCommunityInfoWithCache(String communityId);
    
    /**
     * 获取订阅状态（带缓存）
     * @param userId 用户ID
     * @return 订阅状态
     */
    Map<String, Object> getSubscribeStatusWithCache(String userId);
}
