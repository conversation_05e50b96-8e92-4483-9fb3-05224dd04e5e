-- 车牌识别记录表
DROP TABLE IF EXISTS `eh_plate_recognition_log`;
CREATE TABLE `eh_plate_recognition_log` (
  `log_id` varchar(32) NOT NULL COMMENT '记录ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户姓名',
  `user_phone` varchar(20) DEFAULT NULL COMMENT '用户手机号',
  `file_id` varchar(32) DEFAULT NULL COMMENT '上传文件ID',
  `plate_number` varchar(20) DEFAULT NULL COMMENT '识别出的车牌号',
  `recognition_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '识别状态(0:失败 1:成功)',
  `confidence` decimal(5,4) DEFAULT NULL COMMENT '识别置信度',
  `plate_color` varchar(20) DEFAULT NULL COMMENT '车牌颜色',
  `owner_found` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否找到车主(0:否 1:是)',
  `owner_id` varchar(32) DEFAULT NULL COMMENT '车主ID',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '车主姓名',
  `owner_phone` varchar(20) DEFAULT NULL COMMENT '车主电话',
  `image_size` int(11) DEFAULT NULL COMMENT '图片大小(字节)',
  `recognition_time` int(11) DEFAULT NULL COMMENT '识别耗时(毫秒)',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  PRIMARY KEY (`log_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_plate_number` (`plate_number`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_recognition_status` (`recognition_status`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车牌识别记录表';
