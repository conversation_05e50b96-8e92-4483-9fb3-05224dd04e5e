<view class="orders-container">
  <!-- Tab切换 -->
  <van-tabs active="{{activeTab}}" bind:change="onTabChange" sticky>
    <van-tab wx:for="{{tabs}}" wx:key="key" title="{{item.title}}">
      <!-- 报修列表 -->
      <view wx:if="{{item.key === 'repair'}}" class="tab-content">
        <view wx:if="{{loading && repairList.length === 0}}" class="loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </view>

        <view wx:else class="order-list">
          <view wx:for="{{repairList}}" wx:key="id" class="order-item" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="item-header">
              <view class="item-type">{{item.type || '报修'}}</view>
              <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="item-content">{{item.contentSummary}}</view>
            <view class="item-info">
              <view class="info-left">
                <text class="info-user">{{item.name}}</text>
                <text class="info-phone">{{item.phone}}</text>
              </view>
              <view class="info-right">
                <text class="info-time">{{item.create_time}}</text>
              </view>
            </view>
            <view wx:if="{{item.address}}" class="item-address">
              <van-icon name="location-o" size="12px" />
              <text>{{item.address}}</text>
            </view>
          </view>

          <view wx:if="{{repairList.length === 0 && !loading}}" class="empty">
            <van-empty description="暂无报修单" />
          </view>

          <view wx:if="{{loading && repairList.length > 0}}" class="loading-more">
            <van-loading type="spinner" size="16px">加载更多...</van-loading>
          </view>

          <view wx:if="{{!hasMore && repairList.length > 0}}" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>

      <!-- 投诉建议列表 -->
      <view wx:if="{{item.key === 'complaint'}}" class="tab-content">
        <view wx:if="{{loading && complaintList.length === 0}}" class="loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </view>

        <view wx:else class="order-list">
          <view wx:for="{{complaintList}}" wx:key="id" class="order-item" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="item-header">
              <view class="item-type">{{item.type || '投诉建议'}}</view>
              <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="item-content">{{item.contentSummary}}</view>
            <view class="item-info">
              <view class="info-left">
                <text class="info-user">{{item.name}}</text>
                <text class="info-phone">{{item.phone}}</text>
              </view>
              <view class="info-right">
                <text class="info-time">{{item.create_time}}</text>
              </view>
            </view>
            <view wx:if="{{item.address}}" class="item-address">
              <van-icon name="location-o" size="12px" />
              <text>{{item.address}}</text>
            </view>
          </view>

          <view wx:if="{{complaintList.length === 0 && !loading}}" class="empty">
            <van-empty description="暂无投诉建议" />
          </view>

          <view wx:if="{{loading && complaintList.length > 0}}" class="loading-more">
            <van-loading type="spinner" size="16px">加载更多...</van-loading>
          </view>

          <view wx:if="{{!hasMore && complaintList.length > 0}}" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>
