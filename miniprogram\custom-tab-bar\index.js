// TabBar 配置（支持动态显示）
const TABBAR_CONFIGS = {
  '1': [ // 业主
    {
      text: "首页",
      icon: "wap-home-o",
      pagePath: "/pages/index/index",
      required: true // 必需项，始终显示
    }, {
      text: "信息公开",
      icon: "bullhorn-o",
      pagePath: "/pages/service/index",
      required: true
    }, {
      text: "工作照片",
      icon: "photo-o",
      pagePath: "/pages/markicam/index",
      configKey: "enable_markicam" // 根据配置显示
    }, {
      text: "收支公示",
      icon: "balance-list-o",
      pagePath: "/pages/finance/finance",
      configKey: "enable_financial" // 根据配置显示
    }, {
      text: "我的",
      icon: "user-o",
      pagePath: "/pages/mine/index",
      required: true
    }
  ],
  '2': [ // 物业
    {
      text: "工作台",
      icon: "apps-o",
      pagePath: "/pages/property/dashboard/index"
    }, {
      text: "工单",
      icon: "orders-o",
      pagePath: "/pages/property/orders/index"
    }, {
      text: "业主",
      icon: "contact",
      pagePath: "/pages/property/owners/index"
    }, {
      text: "巡检",
      icon: "bulb-o",
      pagePath: "/pages/property/patrol/index"
    }, {
      text: "我的",
      icon: "user-o",
      pagePath: "/pages/property/profile/index"
    }
  ]
}

Component({
  data: {
    active: -1,
    list: [], // 动态生成
    currentUserType: '1', // 当前用户类型
    filteredCache: {} // 过滤结果缓存
  },

  lifetimes: {
    // 移除自动初始化，完全由页面控制TabBar设置
  },

  methods: {
    onChange(event) {
      const index = event.detail
      const { list } = this.data

      this.setData({
        active: index
      })

      wx.switchTab({
        url: list[index].pagePath
      })
    },

    /**
     * 根据配置过滤菜单项
     * @param {string} userType 用户类型
     * @returns {Array} 过滤后的菜单列表
     */
    filterMenuByConfig(userType) {
      // 检查缓存
      const cacheKey = userType
      if (this.data.filteredCache[cacheKey]) {
        return this.data.filteredCache[cacheKey]
      }

      const originalList = TABBAR_CONFIGS[userType] || TABBAR_CONFIGS['1']

      // 物业用户不需要过滤
      if (userType === '2') {
        this.data.filteredCache[cacheKey] = originalList
        return originalList
      }

      // 业主用户需要根据配置过滤
      try {
        const { getConfigManager } = require('../utils/configManager.js')
        const configManager = getConfigManager()

        const filteredList = originalList.filter(item => {
          // 必需项始终显示
          if (item.required) {
            return true
          }

          // 有配置键的项根据配置决定
          if (item.configKey) {
            return configManager.isEnabled(item.configKey)
          }

          // 默认显示
          return true
        })

        // 缓存结果
        this.data.filteredCache[cacheKey] = filteredList
        return filteredList
      } catch (error) {
        console.error('[TabBar] 配置过滤失败:', error)
        // 出错时返回原始配置
        return originalList
      }
    },

    /**
     * 初始化TabBar（官方推荐方式，支持动态配置）
     * 在每个TabBar页面的onShow中调用
     */
    init() {
      // 安全检查：确保页面栈存在且不为空
      const pages = getCurrentPages()
      if (!pages || pages.length === 0) {
        return
      }

      // 安全检查：确保当前页面存在且有route属性
      const page = pages[pages.length - 1]
      if (!page || !page.route) {
        return
      }

      const currentRoute = `/${page.route}`
      const { list } = this.data
      const active = list.findIndex(v => v.pagePath === currentRoute)

      this.setData({
        active: active >= 0 ? active : -1
      })
    },

    /**
     * 根据用户类型切换TabBar（支持动态配置）
     * @param {string} userType 用户类型 '1'=业主, '2'=物业
     */
    toggleMenu(userType) {
      try {
        // 清除缓存，确保获取最新配置
        this.data.filteredCache = {}

        // 根据配置过滤菜单
        const filteredList = this.filterMenuByConfig(userType)

        this.setData({
          list: filteredList,
          currentUserType: userType
        })

        // 重新计算当前选中状态
        this.init()
      } catch (error) {
        console.error('[TabBar] 切换菜单失败:', error)
      }
    }
  }
})