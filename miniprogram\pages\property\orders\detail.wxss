.detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.detail-content {
  padding: 20rpx;
}

.info-card,
.contact-card,
.attachment-card,
.process-card {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.type-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.header-right {
  text-align: right;
}

.time-text {
  font-size: 24rpx;
  color: #999;
}

.card-content {
  padding: 30rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.address-text {
  font-size: 24rpx;
  color: #666;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-info,
.process-info {
  padding: 30rpx;
}

.contact-item,
.process-item {
  display: flex;
  margin-bottom: 15rpx;
}

.contact-item:last-child,
.process-item:last-child {
  margin-bottom: 0;
}

.contact-label,
.process-label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.contact-value,
.process-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.phone-link {
  color: #1989fa;
  text-decoration: underline;
}

.feedback-text {
  line-height: 1.6;
}

.attachment-list {
  padding: 30rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.attachment-item {
  aspect-ratio: 1;
}

.attachment-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  display: flex;
  gap: 20rpx;
}

.action-buttons .van-button {
  flex: 1;
}

.feedback-btn {
  background-color: #f8f9fa !important;
  color: #333 !important;
  border: 1rpx solid #e9ecef !important;
}

.dialog-content {
  padding: 30rpx;
}

.dialog-content .van-radio {
  margin-bottom: 20rpx;
}

.dialog-content .van-field {
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
}
