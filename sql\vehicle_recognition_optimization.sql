-- 车牌识别功能数据库优化脚本

-- 1. 为车牌号字段添加索引，提升查询性能
ALTER TABLE eh_vehicle ADD INDEX idx_plate_no (plate_no);

-- 2. 为社区ID和车牌号组合添加复合索引
ALTER TABLE eh_vehicle ADD INDEX idx_community_plate (community_id, plate_no);

-- 3. 添加车牌识别菜单配置（可选，也可以通过后台管理界面添加）
-- 注意：需要根据实际的社区ID替换 community_id 字段
INSERT INTO eh_wx_nav (nav_id, parent_id, nav_name, nav_type, tap_name, icon_name, sort_order, status, create_time, update_time, remark, community_id)
VALUES
('nav_vehicle_recognize', '0', '车牌识别挪车', 'page', 'goToVehicleRecognize', 'scan', 100, '0', NOW(), NOW(), '车牌识别挪车功能，支持拍照识别车牌并联系车主', 'your_community_id');

-- 4. 检查车辆表数据完整性
-- 确保车主信息字段不为空的记录有正确的数据
UPDATE eh_vehicle 
SET owner_real_name = COALESCE(owner_real_name, ''), 
    owner_phone = COALESCE(owner_phone, ''),
    house_name = COALESCE(house_name, ''),
    parking_space = COALESCE(parking_space, '')
WHERE community_id IS NOT NULL;

-- 5. 添加车牌号格式验证（可选）
-- 这里可以添加触发器来验证车牌号格式，但为了简化暂时跳过

-- 5. 插入测试车辆数据（可选，用于功能测试）
-- 注意：需要根据实际的社区ID替换 community_id 字段
INSERT IGNORE INTO eh_vehicle (vehicle_id, community_id, plate_no, owner_real_name, owner_phone, house_name, parking_space, vehicle_brand, vehicle_model, check_status, create_time, update_time)
VALUES
('test_vehicle_001', 'your_community_id', '湘LZ0170', '张三', '13800138000', '1号楼101室', 'A-001', '奔驰', 'C200', 1, NOW(), NOW()),
('test_vehicle_002', 'your_community_id', '京A12345', '李四', '13900139000', '2号楼201室', 'B-002', '宝马', 'X5', 1, NOW(), NOW()),
('test_vehicle_003', 'your_community_id', '粤B67890', '王五', '13700137000', '3号楼301室', 'C-003', '奥迪', 'A6', 1, NOW(), NOW());

-- 查询统计信息
SELECT
    '车辆总数' as item,
    COUNT(*) as count
FROM eh_vehicle
UNION ALL
SELECT
    '有车主信息的车辆数',
    COUNT(*)
FROM eh_vehicle
WHERE owner_real_name IS NOT NULL AND owner_real_name != ''
UNION ALL
SELECT
    '有联系电话的车辆数',
    COUNT(*)
FROM eh_vehicle
WHERE owner_phone IS NOT NULL AND owner_phone != '';
