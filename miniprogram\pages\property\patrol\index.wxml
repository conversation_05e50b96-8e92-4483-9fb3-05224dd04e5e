<view class="patrol-container">
  <!-- 筛选按钮 -->
  <view class="filter-tabs">
    <view class="filter-tab {{filterStatus === 'pending' ? 'active' : ''}}"
          bindtap="onFilterChange" data-status="pending">
      待巡更
    </view>
    <view class="filter-tab {{filterStatus === 'completed' ? 'active' : ''}}"
          bindtap="onFilterChange" data-status="completed">
      已完成
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 任务列表 -->
  <view wx:else class="task-list">
    <wxs module="filter">
      var getFilteredTasks = function(taskList, filterStatus) {
        if (!taskList) return []
        if (filterStatus === 'pending') {
          return taskList.filter(function(task) { return task.status === 0 })
        } else if (filterStatus === 'completed') {
          return taskList.filter(function(task) { return task.status === 1 })
        }
        return taskList
      }
      module.exports.getFilteredTasks = getFilteredTasks
    </wxs>

    <view wx:for="{{filter.getFilteredTasks(taskList, filterStatus)}}" wx:key="record_id" class="task-item">
      <view class="task-content">
        <view class="task-header">
          <text class="location-name">{{item.location_name}}</text>
          <view class="task-status {{getStatusClass(item.status)}}">
            {{getStatusText(item.status)}}
          </view>
        </view>

        <view class="task-time">
          <text class="time-label">计划时间：</text>
          <text class="time-value">{{item.planned_time}}</text>
        </view>

        <view class="task-actions">
          <view wx:if="{{item.status === 0}}" class="action-btn primary"
                bindtap="startPatrol" data-id="{{item.record_id}}">
            开始巡更
          </view>
          <view wx:if="{{item.status === 1}}" class="action-btn secondary"
                bindtap="viewPatrolDetail" data-id="{{item.record_id}}">
            查看详情
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{filter.getFilteredTasks(taskList, filterStatus).length === 0}}" class="empty-state">
      <van-icon name="smile-o" size="80rpx" color="#ddd" />
      <text class="empty-text">{{filterStatus === 'pending' ? '暂无待巡更任务' : '暂无已完成任务'}}</text>
    </view>
  </view>
</view>
