package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.service.WxMessageService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 物业管理控制器
 * 提供小程序物业人员管理报修和投诉的接口
 */
@RestController
@RequestMapping("/api/wx/property")
public class PropertyManageController extends BaseWxController {

    private static final Logger logger = LoggerFactory.getLogger(PropertyManageController.class);

    @Autowired
    private WxMessageService wxMessageService;

    /**
     * 获取工作台统计数据
     */
    @PostMapping("/stats")
    @ResponseBody
    public AjaxResult getWorkStats() {
        try {
            // 验证物业人员权限
            if (!isPropertyUser()) {
                return AjaxResult.error("权限不足");
            }

            String communityId = getCurrentUser().getCommunityId();
            Map<String, Object> stats = new HashMap<>();

            // 报修统计
            Record repairStats = Db.findFirst(
                "SELECT " +
                "COUNT(CASE WHEN status = 0 THEN 1 END) as pending_repair, " +
                "COUNT(CASE WHEN status = 1 THEN 1 END) as processing_repair, " +
                "COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_repair " +
                "FROM eh_wx_bx WHERE community_id = ?", communityId);

            // 投诉统计
            Record complaintStats = Db.findFirst(
                "SELECT " +
                "COUNT(CASE WHEN status = 0 THEN 1 END) as pending_complaint, " +
                "COUNT(CASE WHEN status = 1 THEN 1 END) as processing_complaint, " +
                "COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_complaint " +
                "FROM eh_wx_complaint WHERE community_id = ?", communityId);

            stats.put("pendingRepair", repairStats.getInt("pending_repair"));
            stats.put("processingRepair", repairStats.getInt("processing_repair"));
            stats.put("todayRepair", repairStats.getInt("today_repair"));
            stats.put("pendingComplaint", complaintStats.getInt("pending_complaint"));
            stats.put("processingComplaint", complaintStats.getInt("processing_complaint"));
            stats.put("todayComplaint", complaintStats.getInt("today_complaint"));

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取工作台统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取报修单列表
     */
    @PostMapping("/repair/list")
    @ResponseBody
    public AjaxResult getRepairList() {
        try {
            // 验证物业人员权限
            if (!isPropertyUser()) {
                return AjaxResult.error("权限不足");
            }

            JSONObject params = getParams();
            String communityId = getCurrentUser().getCommunityId();

            // 获取筛选参数
            String status = params.getString("status");
            String startDate = params.getString("startDate");
            String endDate = params.getString("endDate");

            // 构建基础查询条件
            EasySQL baseSql = new EasySQL();
            baseSql.append(communityId,"FROM eh_wx_bx WHERE community_id = ? ");

            if (StringUtils.isNotEmpty(status)) {
                baseSql.append(status,"AND status = ? ");
            }
            if (StringUtils.isNotEmpty(startDate)) {
                baseSql.append(startDate,"AND DATE(create_time) >= ? ");
            }
            if (StringUtils.isNotEmpty(endDate)) {
                baseSql.append(endDate,"AND DATE(create_time) <= ? ");
            }

            baseSql.append("ORDER BY create_time DESC");

            int pageNum = params.getIntValue("pageNum");
            int pageSize = params.getIntValue("pageSize");
            if (pageNum <= 0) pageNum = 1;
            if (pageSize <= 0) pageSize = 10;

            Page<Record> page = Db.paginate(
                pageNum,
                pageSize,
                "SELECT id, type, content, address, name, phone, create_time, status, handler, handling_time, feedback",
                baseSql.getSQL(),baseSql.getParams()
            );

            // 处理返回数据
            List<Map<String, Object>> list = new ArrayList<>();
            for (Record record : page.getList()) {
                Map<String, Object> item = record.toMap();
                item.put("statusText", getStatusText(record.getInt("status")));
                item.put("contentSummary", getContentSummary(record.getStr("content")));
                list.add(item);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("total", page.getTotalRow());
            result.put("pageNum", page.getPageNumber());
            result.put("pageSize", page.getPageSize());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取报修列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取报修列表失败");
        }
    }

    /**
     * 获取投诉建议列表
     */
    @PostMapping("/complaint/list")
    @ResponseBody
    public AjaxResult getComplaintList() {
        try {
            // 验证物业人员权限
            if (!isPropertyUser()) {
                return AjaxResult.error("权限不足");
            }

            JSONObject params = getParams();
            String communityId = getCurrentUser().getCommunityId();

            // 获取筛选参数
            String status = params.getString("status");
            String startDate = params.getString("startDate");
            String endDate = params.getString("endDate");

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(communityId);

            // 构建基础查询条件
            StringBuilder baseSql = new StringBuilder();
            baseSql.append("FROM eh_wx_complaint WHERE community_id = ? ");

            if (StringUtils.isNotEmpty(status)) {
                baseSql.append("AND status = ? ");
            }
            if (StringUtils.isNotEmpty(startDate)) {
                baseSql.append("AND DATE(STR_TO_DATE(create_time, '%Y-%m-%d %H:%i:%s')) >= ? ");
            }
            if (StringUtils.isNotEmpty(endDate)) {
                baseSql.append("AND DATE(STR_TO_DATE(create_time, '%Y-%m-%d %H:%i:%s')) <= ? ");
            }

            int pageNum = params.getIntValue("pageNum");
            int pageSize = params.getIntValue("pageSize");
            if (pageNum <= 0) pageNum = 1;
            if (pageSize <= 0) pageSize = 10;

            Page<Record> page = Db.paginate(
                pageNum,
                pageSize,
                "SELECT id, type, content, address, name, phone, create_time, status, handler, handling_time, feedback",
                baseSql.toString() + " ORDER BY create_time DESC",
                sqlParams.toArray()
            );

            // 处理返回数据
            List<Map<String, Object>> list = new ArrayList<>();
            for (Record record : page.getList()) {
                Map<String, Object> item = record.toMap();
                item.put("statusText", getStatusText(record.getInt("status")));
                item.put("contentSummary", getContentSummary(record.getStr("content")));
                list.add(item);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("total", page.getTotalRow());
            result.put("pageNum", page.getPageNumber());
            result.put("pageSize", page.getPageSize());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取投诉列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取投诉列表失败");
        }
    }

    /**
     * 更新报修单状态
     */
    @PostMapping("/repair/updateStatus")
    @ResponseBody
    public AjaxResult updateRepairStatus() {
        try {
            // 验证物业人员权限
            if (!isPropertyUser()) {
                return AjaxResult.error("权限不足");
            }

            JSONObject params = getParams();
            String id = params.getString("id");
            String status = params.getString("status");
            String feedback = params.getString("feedback");

            if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
                return AjaxResult.error("参数错误");
            }

            // 构建更新SQL
            StringBuilder sql = new StringBuilder("UPDATE eh_wx_bx SET status = ?, update_time = ?");
            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(status);
            sqlParams.add(new Date());

            // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
            if ("1".equals(status) || "2".equals(status)) {
                sql.append(", handler = ?, handling_time = ?");
                sqlParams.add(getCurrentUser().getUsername());
                sqlParams.add(new Date());
            }

            // 当状态为已完成(2)且有反馈内容时，记录处理反馈
            if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
                sql.append(", feedback = ?");
                sqlParams.add(feedback);
            }

            sql.append(" WHERE id = ? AND community_id = ?");
            sqlParams.add(id);
            sqlParams.add(getCurrentUser().getCommunityId());

            int result = Db.update(sql.toString(), sqlParams.toArray());
            
            if (result > 0) {
                // 发送状态更新消息通知
                sendStatusUpdateMessage(id, status, feedback, "repair");
                return AjaxResult.success("状态更新成功");
            } else {
                return AjaxResult.error("更新失败，记录不存在");
            }
        } catch (Exception e) {
            logger.error("更新报修状态失败: " + e.getMessage(), e);
            return AjaxResult.error("更新状态失败");
        }
    }

    /**
     * 更新投诉建议状态
     */
    @PostMapping("/complaint/updateStatus")
    @ResponseBody
    public AjaxResult updateComplaintStatus() {
        try {
            // 验证物业人员权限
            if (!isPropertyUser()) {
                return AjaxResult.error("权限不足");
            }

            JSONObject params = getParams();
            String id = params.getString("id");
            String status = params.getString("status");
            String feedback = params.getString("feedback");

            if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
                return AjaxResult.error("参数错误");
            }

            // 构建更新SQL
            StringBuilder sql = new StringBuilder("UPDATE eh_wx_complaint SET status = ?, update_time = ?");
            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(status);
            sqlParams.add(new Date());

            // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
            if ("1".equals(status) || "2".equals(status)) {
                sql.append(", handler = ?, handling_time = ?");
                sqlParams.add(getCurrentUser().getUsername());
                sqlParams.add(new Date());
            }

            // 当状态为已完成(2)且有反馈内容时，记录处理反馈
            if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
                sql.append(", feedback = ?");
                sqlParams.add(feedback);
            }

            sql.append(" WHERE id = ? AND community_id = ?");
            sqlParams.add(id);
            sqlParams.add(getCurrentUser().getCommunityId());

            int result = Db.update(sql.toString(), sqlParams.toArray());
            
            if (result > 0) {
                // 发送状态更新消息通知
                sendStatusUpdateMessage(id, status, feedback, "complaint");
                return AjaxResult.success("状态更新成功");
            } else {
                return AjaxResult.error("更新失败，记录不存在");
            }
        } catch (Exception e) {
            logger.error("更新投诉状态失败: " + e.getMessage(), e);
            return AjaxResult.error("更新状态失败");
        }
    }

    /**
     * 验证是否为物业用户
     */
    private boolean isPropertyUser() {
        try {
            // 检查用户类型，物业用户的userType应该为2
            return "2".equals(getCurrentUser().getUserType());
        } catch (Exception e) {
            logger.warn("验证物业用户权限失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待处理";
            case 1: return "处理中";
            case 2: return "已完成";
            default: return "未知";
        }
    }

    /**
     * 获取内容摘要
     */
    private String getContentSummary(String content) {
        if (StringUtils.isEmpty(content)) return "";
        return content.length() > 50 ? content.substring(0, 50) + "..." : content;
    }

    /**
     * 发送状态更新消息通知
     */
    private void sendStatusUpdateMessage(String id, String status, String feedback, String type) {
        try {
            // 这里可以调用微信消息服务发送通知
            // wxMessageService.sendStatusUpdateNotice(...);
            logger.info("发送状态更新通知: id={}, status={}, type={}", id, status, type);
        } catch (Exception e) {
            logger.warn("发送状态更新通知失败: " + e.getMessage());
        }
    }
}
