// 工单详情页面
const app = getApp()

Page({
  data: {
    id: '',
    type: '', // repair 或 complaint
    detail: null,
    loading: true,
    processing: false,
    showStatusDialog: false,
    showFeedbackDialog: false,
    statusOptions: [
      { value: '0', label: '待处理' },
      { value: '1', label: '处理中' },
      { value: '2', label: '已完成' }
    ],
    selectedStatus: '',
    feedback: '',
    attachments: []
  },

  onLoad(options) {
    const { id, type } = options
    if (!id || !type) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ id, type })
    this.loadDetail()
    this.loadAttachments()
  },

  // 加载详情
  async loadDetail() {
    this.setData({ loading: true })
    try {
      const url = this.data.type === 'repair' 
        ? '/api/wx/bx/bxDetail' 
        : '/api/wx/complaint/detail'
      
      const res = await app.request({
        url,
        method: 'POST',
        data: { id: this.data.id }
      })

      if (res.code === 0 && res.data) {
        this.setData({
          detail: res.data,
          selectedStatus: res.data.status?.toString() || '0'
        })
        
        // 设置页面标题
        const title = this.data.type === 'repair' ? '报修详情' : '投诉建议详情'
        wx.setNavigationBarTitle({ title })
      } else {
        throw new Error(res.msg || '获取详情失败')
      }
    } catch (error) {
      console.error('加载详情失败:', error)
      wx.showToast({
        title: '加载详情失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载附件
  async loadAttachments() {
    try {
      const url = this.data.type === 'repair' 
        ? '/api/wx/bx/getAttachments' 
        : '/api/wx/complaint/getAttachments'
      
      const res = await app.request({
        url,
        method: 'POST',
        data: { businessId: this.data.id }
      })

      if (res.code === 0) {
        this.setData({
          attachments: res.data || []
        })
      }
    } catch (error) {
      console.error('加载附件失败:', error)
    }
  },

  // 显示状态更新对话框
  showStatusUpdate() {
    this.setData({ showStatusDialog: true })
  },

  // 状态选择
  onStatusChange(e) {
    this.setData({ selectedStatus: e.detail })
  },

  // 确认状态更新
  async confirmStatusUpdate() {
    if (this.data.processing) return
    
    this.setData({ processing: true })
    try {
      const url = this.data.type === 'repair' 
        ? '/api/wx/property/repair/updateStatus' 
        : '/api/wx/property/complaint/updateStatus'
      
      const res = await app.request({
        url,
        method: 'POST',
        data: {
          id: this.data.id,
          status: this.data.selectedStatus,
          feedback: this.data.feedback
        }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
        this.setData({ 
          showStatusDialog: false,
          showFeedbackDialog: false,
          feedback: ''
        })
        // 重新加载详情
        this.loadDetail()
      } else {
        throw new Error(res.msg || '更新失败')
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      wx.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      })
    } finally {
      this.setData({ processing: false })
    }
  },

  // 显示反馈对话框
  showFeedbackInput() {
    this.setData({ showFeedbackDialog: true })
  },

  // 反馈输入
  onFeedbackInput(e) {
    this.setData({ feedback: e.detail })
  },

  // 预览图片
  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls: urls || [url]
    })
  },

  // 拨打电话
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      })
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待处理',
      1: '处理中', 
      2: '已完成'
    }
    return statusMap[status] || '未知'
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      0: '#ff976a',
      1: '#1989fa',
      2: '#07c160'
    }
    return colorMap[status] || '#999'
  },

  // 关闭对话框
  closeDialog() {
    this.setData({ 
      showStatusDialog: false,
      showFeedbackDialog: false 
    })
  }
})
