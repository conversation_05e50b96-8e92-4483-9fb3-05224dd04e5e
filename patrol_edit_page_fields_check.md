# 巡更配置编辑页面字段检查

## 字段存在性确认

### ✅ 新增页面 (add.html) 字段
1. **地点名称** (第37-41行) - 必填文本框
2. **地点地址** (第42-46行) - 可选文本框
3. **计划时间** (第50-70行) - 必填时间选择器 + 快速选择按钮
4. **巡更人员** (第103-111行) - 必填 select2 多选组件
5. **状态** (第112-124行) - 单选按钮，默认"启用"
6. **备注说明** (第125-130行) - 可选文本域

### ✅ 编辑页面 (edit.html) 字段
1. **地点名称** (第33-37行) - 必填文本框，显示现有值
2. **地点地址** (第38-42行) - 可选文本框，显示现有值
3. **计划时间** (第46-66行) - 必填时间选择器 + 快速选择按钮，显示现有值
4. **巡更人员** (第100-107行) - 必填 select2 多选组件，加载现有用户
5. **状态** (第108-120行) - 单选按钮，根据现有值选中
6. **备注说明** (第121-126行) - 可选文本域，显示现有值

## 可能的显示问题排查

### 1. 浏览器缓存问题
**解决方案**：
- 清除浏览器缓存
- 强制刷新页面 (Ctrl+F5)
- 使用无痕模式访问

### 2. CSS 样式问题
**检查要点**：
- 字段是否被 `display: none` 隐藏
- 字段是否被其他元素遮挡
- 表单容器是否正常显示

### 3. JavaScript 错误
**检查要点**：
- 打开浏览器开发者工具查看控制台错误
- 检查 select2 组件是否正确初始化
- 检查数据加载是否成功

### 4. 数据加载问题
**编辑页面特有问题**：
- 检查 `patrolConfig` 对象是否正确传递到模板
- 检查用户数据是否正确加载到 select2

## 调试步骤

### 1. 检查页面源码
```html
<!-- 在浏览器中查看页面源码，确认字段是否存在 -->
<div class="form-group">
    <label class="col-sm-3 control-label is-required">巡更人员：</label>
    <div class="col-sm-8">
        <select id="patrolUsers" name="patrol_users_select" class="form-control" multiple="multiple">
        </select>
    </div>
</div>
```

### 2. 检查控制台错误
```javascript
// 打开浏览器开发者工具 (F12)
// 查看 Console 标签页是否有错误信息
// 特别关注 select2 相关错误
```

### 3. 检查网络请求
```javascript
// 在 Network 标签页检查：
// 1. 用户列表接口 (/system/user/list) 是否正常返回
// 2. 配置详情接口 (/oc/patrol/config/detail/{id}) 是否正常返回
```

### 4. 手动测试 select2
```javascript
// 在控制台执行以下代码测试 select2 是否正常
$('#patrolUsers').select2('data');
```

## 预期显示效果

### 新增页面
- 所有字段都应该可见且可编辑
- select2 组件应该支持搜索和多选
- 快速时间选择按钮应该正常工作

### 编辑页面
- 所有字段都应该可见且显示现有数据
- select2 组件应该显示已选择的用户
- 状态单选按钮应该选中正确的值
- 备注文本域应该显示现有备注

## 如果字段仍然不显示

请提供以下信息：
1. 浏览器类型和版本
2. 控制台错误信息截图
3. 页面显示效果截图
4. 网络请求状态截图

这将帮助进一步诊断问题。
