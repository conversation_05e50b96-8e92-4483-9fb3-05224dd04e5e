.orders-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.tab-content {
  padding: 20rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.item-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.item-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.status-0 {
  background-color: #ff976a;
}

.status-1 {
  background-color: #1989fa;
}

.status-2 {
  background-color: #07c160;
}

.item-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.info-left {
  display: flex;
  gap: 20rpx;
}

.info-user {
  font-size: 24rpx;
  color: #333;
}

.info-phone {
  font-size: 24rpx;
  color: #1989fa;
}

.info-right {
  font-size: 22rpx;
  color: #999;
}

.item-address {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #999;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 24rpx;
  color: #999;
}
