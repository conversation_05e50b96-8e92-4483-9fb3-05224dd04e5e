package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 微信端业主管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/wx/owner")
public class WxOwnerController extends BaseWxController {

    /**
     * 获取楼栋列表
     */
    @GetMapping("/buildings")
    public AjaxResult getBuildingList() {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            String sql = "SELECT b.building_id, b.name, " +
                        "COALESCE(h.house_count, 0) as house_count " +
                        "FROM eh_building b " +
                        "LEFT JOIN (" +
                        "    SELECT building_id, COUNT(*) as house_count " +
                        "    FROM eh_house_info " +
                        "    WHERE community_id = ? " +
                        "    GROUP BY building_id" +
                        ") h ON b.building_id = h.building_id " +
                        "WHERE b.community_id = ? " +
                        "ORDER BY b.order_index, b.name";

            List<Record> buildings = Db.find(sql, communityId, communityId);
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record building : buildings) {
                Map<String, Object> item = new HashMap<>();
                item.put("buildingId", building.getStr("building_id"));
                item.put("name", building.getStr("name"));
                item.put("houseCount", building.getInt("house_count"));
                result.add(item);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取楼栋列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取楼栋列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定楼栋的房屋列表（支持分页）
     */
    @GetMapping("/houses/{buildingId}")
    public AjaxResult getHouseList(@PathVariable String buildingId,
                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "20") Integer pageSize) {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            if (StringUtils.isEmpty(buildingId)) {
                return AjaxResult.error("楼栋ID不能为空");
            }

            String sql = "SELECT h.house_id, h.room, h.floor, h.use_area, h.house_status, " +
                        "h.combina_name, " +
                        "COALESCE(o.owner_count, 0) as owner_count, " +
                        "COALESCE(o.owner_names, '') as owner_names " +
                        "FROM eh_house_info h " +
                        "LEFT JOIN (" +
                        "    SELECT r.house_id, COUNT(*) as owner_count, " +
                        "    GROUP_CONCAT(ow.owner_name SEPARATOR ',') as owner_names " +
                        "    FROM eh_house_owner_rel r " +
                        "    LEFT JOIN eh_owner ow ON r.owner_id = ow.owner_id " +
                        "    WHERE r.check_status = 1 " +
                        "    GROUP BY r.house_id" +
                        ") o ON h.house_id = o.house_id " +
                        "WHERE h.building_id = ? AND h.community_id = ? " +
                        "ORDER BY h.floor, h.room " +
                        "LIMIT ? OFFSET ?";

            int offset = (pageNum - 1) * pageSize;
            List<Record> houses = Db.find(sql, buildingId, communityId, pageSize, offset);

            List<Map<String, Object>> result = new ArrayList<>();
            for (Record house : houses) {
                Map<String, Object> item = new HashMap<>();
                item.put("houseId", house.getStr("house_id"));
                item.put("room", house.getStr("room"));
                item.put("floor", house.getStr("floor"));
                item.put("useArea", house.getBigDecimal("use_area"));
                item.put("houseStatus", house.getStr("house_status"));
                item.put("combinaName", house.getStr("combina_name"));
                item.put("ownerCount", house.getInt("owner_count"));
                item.put("ownerNames", house.getStr("owner_names"));
                result.add(item);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取房屋列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取房屋详情和业主信息
     */
    @GetMapping("/houseDetail/{houseId}")
    public AjaxResult getHouseDetail(@PathVariable String houseId) {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }
            
            if (StringUtils.isEmpty(houseId)) {
                return AjaxResult.error("房屋ID不能为空");
            }

            // 获取房屋基本信息
            String houseSql = "SELECT h.*, b.name as building_name, u.name as unit_name " +
                             "FROM eh_house_info h " +
                             "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                             "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                             "WHERE h.house_id = ? AND h.community_id = ?";
            
            Record houseInfo = Db.findFirst(houseSql, houseId, communityId);
            if (houseInfo == null) {
                return AjaxResult.error("房屋不存在");
            }

            // 获取绑定的业主列表
            String ownerSql = "SELECT r.rel_id, r.rel_type, r.is_default, r.create_time, " +
                             "o.owner_id, o.owner_name, o.mobile, o.id_card, o.role " +
                             "FROM eh_house_owner_rel r " +
                             "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                             "WHERE r.house_id = ? AND r.check_status = 1 " +
                             "ORDER BY r.is_default DESC, r.create_time DESC";

            List<Record> ownerList = Db.find(ownerSql, houseId);

            // 获取绑定的车位列表
            String parkingSql = "SELECT p.parking_id, p.parking_no, p.parking_type, p.parking_status, p.parking_area, p.create_time FROM eh_parking_space p WHERE p.house_id = ? ORDER BY p.parking_no";

            List<Record> parkingList = Db.find(parkingSql, houseId);

            // 获取绑定的车辆列表
            String vehicleSql = "SELECT v.vehicle_id, v.plate_no, v.vehicle_type, v.vehicle_brand, " +
                               "v.vehicle_model, v.owner_real_name, v.create_time " +
                               "FROM eh_vehicle v LEFT JOIN eh_vehicle_house_rel o ON v.vehicle_id = o.vehicle_id " +
                               "WHERE o.house_id = ? ORDER BY v.create_time DESC";

            List<Record> vehicleList = Db.find(vehicleSql, houseId);

            Map<String, Object> result = new HashMap<>();
            result.put("houseInfo", houseInfo.toMap());
            result.put("ownerList", recordToMap(ownerList));
            result.put("parkingList", recordToMap(parkingList));
            result.put("vehicleList", recordToMap(vehicleList));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋详情失败: " + e.getMessage(), e);
            return AjaxResult.error("获取房屋详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索楼栋
     */
    @GetMapping("/searchBuildings")
    public AjaxResult searchBuildings(@RequestParam String keyword) {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }
            
            if (StringUtils.isEmpty(keyword)) {
                // 如果搜索关键词为空，返回所有楼栋
                return getBuildingList();
            }

            String sql = "SELECT b.building_id, b.name, " +
                        "COALESCE(h.house_count, 0) as house_count " +
                        "FROM eh_building b " +
                        "LEFT JOIN (" +
                        "    SELECT building_id, COUNT(*) as house_count " +
                        "    FROM eh_house_info " +
                        "    WHERE community_id = ? " +
                        "    GROUP BY building_id" +
                        ") h ON b.building_id = h.building_id " +
                        "WHERE b.community_id = ? AND b.name LIKE ? " +
                        "ORDER BY b.order_index, b.name";

            List<Record> buildings = Db.find(sql, communityId, communityId, "%" + keyword + "%");
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record building : buildings) {
                Map<String, Object> item = new HashMap<>();
                item.put("buildingId", building.getStr("building_id"));
                item.put("name", building.getStr("name"));
                item.put("houseCount", building.getInt("house_count"));
                result.add(item);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("搜索楼栋失败: " + e.getMessage(), e);
            return AjaxResult.error("搜索楼栋失败: " + e.getMessage());
        }
    }
}
