@echo off
echo 正在执行巡更功能数据库表创建脚本...
echo.

REM 请根据实际情况修改数据库连接信息
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=ehome
set DB_USER=root
set DB_PASS=123456

echo 连接数据库: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo 用户: %DB_USER%
echo.

REM 执行SQL脚本
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% < create_patrol_tables.sql

if %errorlevel% equ 0 (
    echo.
    echo ✓ 巡更功能数据库表创建成功！
    echo.
    echo 已创建的表：
    echo - eh_patrol_config          巡更配置表
    echo - eh_patrol_config_user     巡更人员配置表  
    echo - eh_patrol_record          巡更记录表
    echo - eh_patrol_statistics      巡更统计表
    echo.
    echo 接下来请：
    echo 1. 重启应用服务器
    echo 2. 在后台管理系统中配置巡更地点和人员
    echo 3. 生成今日巡更任务
    echo 4. 使用小程序进行巡更测试
) else (
    echo.
    echo ✗ 数据库表创建失败！
    echo 请检查：
    echo 1. MySQL服务是否启动
    echo 2. 数据库连接信息是否正确
    echo 3. 用户是否有足够的权限
    echo 4. 数据库 %DB_NAME% 是否存在
)

echo.
pause
