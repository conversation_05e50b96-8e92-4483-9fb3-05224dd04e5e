const app = getApp()
import { createFeedbackManager } from '../../utils/feedbackManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'

const feedbackManager = createFeedbackManager('authApply')
const loadingManager = getLoadingManager()

Page({
  data: {
    // 表单数据
    ownerName: '',
    idCard: '',
    mobile: '',
    relType: '1', // 默认选择业主
    gender: '', // 性别
    isLive: '', // 住户状态
    moveDate: '', // 入住日期
    remark: '', // 备注

    // 房屋选择相关
    selectedCommunityId: '',
    selectedBuildingId: '',
    selectedHouseId: '',
    selectedHouseText: '',

    // 级联选择器
    showCascaderPopup: false,
    cascaderValue: [],
    cascaderOptions: [],

    // 新字段选择器
    showGenderPicker: false,
    showIsLivePicker: false,
    showDatePicker: false,
    showRelTypePicker: false,

    // 选择器选项
    genderOptions: ['男', '女'],
    isLiveOptions: ['未入住', '已入住', '已迁出'],
    genderActionOptions: [
      { name: '男', value: 'M' },
      { name: '女', value: 'F' }
    ],
    isLiveActionOptions: [
      { name: '未入住', value: '0' },
      { name: '已入住', value: '1' },
      { name: '已迁出', value: '2' }
    ],
    relTypeActionOptions: [
      { name: '业主', value: '1' },
      { name: '家庭成员', value: '2' },
      { name: '租户', value: '3' }
    ],

    // 显示文本
    genderText: '',
    isLiveText: '',
    relTypeText: '业主', // 默认显示业主
    currentDate: new Date().getTime(),

    // 状态
    submitting: false,
    canSubmit: false,

    // 附件上传
    attachmentFiles: [],
    businessId: null
  },

  onLoad() {
    this.loadCommunities()
    this.loadUserInfo()
    this.initBusinessId()
  },

  // 初始化业务ID
  initBusinessId() {
    const businessId = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    this.setData({ businessId })
  },

  // 加载用户信息
  loadUserInfo() {
    try {
      const wxUserInfo = wx.getStorageSync('wxUserInfo')
      if (wxUserInfo && wxUserInfo.mobile) {
        this.setData({
          mobile: wxUserInfo.mobile
        })
        this.checkCanSubmit()
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  },

  // 加载小区列表
  async loadCommunities() {
    try {
      const res = await app.request({
        url: '/api/wx/auth/apply/communities',
        method: 'GET'
      })

      if (res.code === 0) {
        const communities = res.data || []
        this.setData({
          cascaderOptions: communities.map(item => ({
            text: item.text,
            value: item.value,
            children: [] // 异步加载
          }))
        })
      } else {
        wx.showToast({
          title: res.msg || '获取小区列表失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载小区列表失败:', error)
      wx.showToast({
        title: '获取小区列表失败',
        icon: 'none'
      })
    }
  },

  // 显示级联选择器
  showCascader() {
    this.setData({ showCascaderPopup: true })
  },

  // 关闭级联选择器
  onClose() {
    this.setData({ showCascaderPopup: false })
  },

  // 级联选择器变化
  async onCascaderChange(event) {
    const { value, tabIndex } = event.detail

    if (tabIndex === 0) {
      // 选择了小区，加载楼栋
      await this.loadBuildings(value)
    } else if (tabIndex === 1) {
      // 选择了楼栋，加载房屋
      await this.loadHouses(value)
    }
  },

  // 级联选择器完成
  onCascaderFinish(event) {
    const { selectedOptions } = event.detail

    // 检查是否选择了三级（小区、楼栋、房屋）
    if (selectedOptions && selectedOptions.length === 3) {
      const houseText = selectedOptions.map(option => option.text).join(' ')

      // 从selectedOptions中提取各级的value
      const communityId = selectedOptions[0].value
      const buildingId = selectedOptions[1].value
      const houseId = selectedOptions[2].value

      this.setData({
        selectedCommunityId: communityId,
        selectedBuildingId: buildingId,
        selectedHouseId: houseId,
        selectedHouseText: houseText,
        cascaderValue: [communityId, buildingId, houseId],
        showCascaderPopup: false
      })
      this.checkCanSubmit()
    }
  },

  // 加载楼栋列表
  async loadBuildings(communityId) {
    try {
      const res = await app.request({
        url: `/api/wx/auth/apply/buildings/${communityId}`,
        method: 'GET'
      })

      if (res.code === 0) {
        const buildings = res.data || []
        const options = [...this.data.cascaderOptions]
        const communityIndex = options.findIndex(item => item.value === communityId)
        
        if (communityIndex !== -1) {
          options[communityIndex].children = buildings.map(item => ({
            text: item.text,
            value: item.value,
            children: [] // 异步加载
          }))

          this.setData({ cascaderOptions: options })
        }
      } else {
        wx.showToast({
          title: res.msg || '获取楼栋列表失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载楼栋列表失败:', error)
      wx.showToast({
        title: '获取楼栋列表失败',
        icon: 'none'
      })
    }
  },

  // 加载房屋列表
  async loadHouses(buildingId) {
    try {
      const res = await app.request({
        url: `/api/wx/auth/apply/houses/${buildingId}`,
        method: 'GET'
      })

      if (res.code === 0) {
        const houses = res.data || []
        const options = [...this.data.cascaderOptions]
        
        // 找到对应的楼栋并更新房屋列表
        for (let community of options) {
          if (community.children) {
            const buildingIndex = community.children.findIndex(item => item.value === buildingId)
            if (buildingIndex !== -1) {
              community.children[buildingIndex].children = houses.map(item => ({
                text: item.text,
                value: item.value
                // 最后一级不需要children属性
              }))
              break
            }
          }
        }

        this.setData({ cascaderOptions: options })
      } else {
        wx.showToast({
          title: res.msg || '获取房屋列表失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载房屋列表失败:', error)
      wx.showToast({
        title: '获取房屋列表失败',
        icon: 'none'
      })
    }
  },



  // 表单输入事件
  onOwnerNameChange(event) {
    this.setData({ ownerName: event.detail })
    this.checkCanSubmit()
  },

  onIdCardChange(event) {
    this.setData({ idCard: event.detail })
    this.checkCanSubmit()
  },

  onMobileChange(event) {
    this.setData({ mobile: event.detail })
    this.checkCanSubmit()
  },

  onRemarkChange(event) {
    this.setData({ remark: event.detail })
  },

  // 性别选择器
  showGenderPicker() {
    this.setData({ showGenderPicker: true })
  },

  onGenderPickerClose() {
    this.setData({ showGenderPicker: false })
  },

  onGenderSelect(event) {
    const { value, name } = event.detail
    this.setData({
      gender: value,
      genderText: name,
      showGenderPicker: false
    })
    this.checkCanSubmit()
  },

  // 住户状态选择器
  showIsLivePicker() {
    this.setData({ showIsLivePicker: true })
  },

  onIsLivePickerClose() {
    this.setData({ showIsLivePicker: false })
  },

  onIsLiveSelect(event) {
    const { value, name } = event.detail
    const updateData = {
      isLive: value,
      isLiveText: name,
      showIsLivePicker: false
    }

    // 如果不是已入住状态，清空入住日期
    if (value !== '1') {
      updateData.moveDate = ''
    }

    this.setData(updateData)
    this.checkCanSubmit()
  },

  // 入住日期选择器
  showDatePicker() {
    this.setData({ showDatePicker: true })
  },

  onDatePickerClose() {
    this.setData({ showDatePicker: false })
  },

  onDateConfirm(event) {
    const date = new Date(event.detail)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const dateStr = `${year}-${month}-${day}`

    this.setData({
      moveDate: dateStr,
      showDatePicker: false
    })
    this.checkCanSubmit()
  },

  // 认证身份选择器
  showRelTypePicker() {
    this.setData({ showRelTypePicker: true })
  },

  onRelTypePickerClose() {
    this.setData({ showRelTypePicker: false })
  },

  onRelTypeSelect(event) {
    const { value, name } = event.detail
    this.setData({
      relType: value,
      relTypeText: name,
      showRelTypePicker: false
    })
    this.checkCanSubmit()
  },

  // 附件上传
  async onAttachmentUpload(e) {
    const { file } = e.detail
    const newFiles = await feedbackManager.handleFileUpload(
      file,
      this.data.attachmentFiles,
      loadingManager,
      handleError,
      this.data.businessId
    )
    this.setData({ attachmentFiles: newFiles })
    this.checkCanSubmit()
  },

  // 附件删除
  async onAttachmentDelete(e) {
    const { index } = e.detail
    const updatedFiles = await feedbackManager.handleFileDelete(index, this.data.attachmentFiles)
    this.setData({ attachmentFiles: updatedFiles })
    this.checkCanSubmit()
  },



  // 检查是否可以提交
  checkCanSubmit() {
    const { ownerName, idCard, mobile, selectedHouseId, gender, isLive, moveDate, relType, attachmentFiles } = this.data
    let canSubmit = ownerName && idCard && mobile && selectedHouseId && gender && isLive && relType && attachmentFiles.length > 0

    // 如果选择了已入住，必须填写入住日期
    if (isLive === '1' && !moveDate) {
      canSubmit = false
    }

    this.setData({ canSubmit })
  },

  // 验证表单
  validateForm() {
    const { ownerName, idCard, mobile, selectedHouseId, gender, isLive, moveDate, relType, attachmentFiles } = this.data

    if (!selectedHouseId) {
      wx.showToast({
        title: '请选择房屋',
        icon: 'none'
      })
      return false
    }

    if (!ownerName || ownerName.trim() === '') {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return false
    }

    if (!idCard || idCard.trim() === '') {
      wx.showToast({
        title: '请输入证件号码',
        icon: 'none'
      })
      return false
    }

    if (!mobile || mobile.trim() === '') {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }

    // 验证手机号格式
    const mobileReg = /^1[3-9]\d{9}$/
    if (!mobileReg.test(mobile)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }

    if (!gender) {
      wx.showToast({
        title: '请选择性别',
        icon: 'none'
      })
      return false
    }

    if (!isLive) {
      wx.showToast({
        title: '请选择住户状态',
        icon: 'none'
      })
      return false
    }

    if (!relType) {
      wx.showToast({
        title: '请选择认证身份',
        icon: 'none'
      })
      return false
    }

    // 验证附件上传
    if (!attachmentFiles || attachmentFiles.length === 0) {
      wx.showToast({
        title: '请上传证件照片',
        icon: 'none'
      })
      return false
    }

    // 如果选择了已入住，必须填写入住日期
    if (isLive === '1' && (!moveDate || moveDate.trim() === '')) {
      wx.showToast({
        title: '已入住状态需要选择入住日期',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 提交申请
  async submitApply() {
    if (!this.data.canSubmit || this.data.submitting) return

    // 表单验证
    if (!this.validateForm()) return

    try {
      this.setData({ submitting: true })

      // 获取附件文件ID
      const attachmentFileId = this.data.attachmentFiles.length > 0 ? this.data.attachmentFiles[0].fileId : null

      const res = await app.request({
        url: '/api/wx/auth/apply/submit',
        method: 'POST',
        data: {
          communityId: this.data.selectedCommunityId,
          houseId: this.data.selectedHouseId,
          ownerName: this.data.ownerName,
          idCard: this.data.idCard,
          mobile: this.data.mobile,
          relType: parseInt(this.data.relType),
          gender: this.data.gender,
          isLive: this.data.isLive,
          moveDate: this.data.moveDate,
          remark: this.data.remark,
          attachmentFileId: attachmentFileId
        }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: res.msg || '申请提交失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交申请失败:', error)
      wx.showToast({
        title: '申请提交失败',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  }
})
