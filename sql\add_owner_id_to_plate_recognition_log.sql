-- 为车牌识别记录表添加owner_id字段
-- 执行时间：2025-08-19

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'eh_plate_recognition_log'
    AND COLUMN_NAME = 'owner_id'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE eh_plate_recognition_log ADD COLUMN owner_id varchar(32) DEFAULT NULL COMMENT ''车主ID'' AFTER owner_found',
    'SELECT ''字段owner_id已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'eh_plate_recognition_log'
    AND INDEX_NAME = 'idx_owner_id'
);

-- 如果索引不存在，则添加
SET @sql = IF(@index_exists = 0,
    'ALTER TABLE eh_plate_recognition_log ADD INDEX idx_owner_id (owner_id)',
    'SELECT ''索引idx_owner_id已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE eh_plate_recognition_log;
