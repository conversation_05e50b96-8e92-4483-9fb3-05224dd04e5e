<view class="patrol-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载详情...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="location-name">{{recordInfo.location_name}}</view>
        <view class="record-status {{getStatusClass(recordInfo.status)}}">
          {{getStatusText(recordInfo.status)}}
        </view>
      </view>
      
      <view class="location-address" wx:if="{{recordInfo.location_address}}">
        {{recordInfo.location_address}}
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">计划时间</text>
          <text class="info-value">{{recordInfo.planned_time}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">巡更日期</text>
          <text class="info-value">{{recordInfo.patrol_date}}</text>
        </view>
        <view class="info-item" wx:if="{{recordInfo.actual_time}}">
          <text class="info-label">完成时间</text>
          <text class="info-value">{{formatTime(recordInfo.actual_time)}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">巡更人员</text>
          <text class="info-value">{{recordInfo.patrol_user_name}}</text>
        </view>
      </view>
    </view>

    <!-- 位置信息卡片 -->
    <view class="location-card" wx:if="{{recordInfo.latitude && recordInfo.longitude}}">
      <view class="card-title">位置信息</view>
      
      <view class="location-info">
        <view class="location-item">
          <text class="label">巡更坐标：</text>
          <text class="value">{{recordInfo.latitude}}, {{recordInfo.longitude}}</text>
        </view>
        <view class="location-item" wx:if="{{recordInfo.location_accuracy}}">
          <text class="label">定位精度：</text>
          <text class="value">{{recordInfo.location_accuracy}}米</text>
        </view>
        <view class="location-item" wx:if="{{recordInfo.distance_from_target}}">
          <text class="label">位置偏差：</text>
          <text class="value {{getDistanceClass(recordInfo.distance_from_target, recordInfo.location_range)}}">
            {{getDistanceText(recordInfo.distance_from_target)}}
          </text>
        </view>
      </view>
      
      <button class="view-location-btn" bindtap="viewLocation">
        <van-icon name="location-o" size="16px" />
        <text>查看位置</text>
      </button>
    </view>

    <!-- 照片信息卡片 -->
    <view class="photo-card" wx:if="{{photoList.length > 0}}">
      <view class="card-title">
        巡更照片
        <text class="photo-count">({{photoList.length}}张)</text>
      </view>
      
      <view class="photo-grid">
        <view wx:for="{{photoList}}" wx:key="fileId" class="photo-item">
          <image class="photo-image" src="{{item.url}}" mode="aspectFill" 
                 bindtap="previewPhoto" data-index="{{index}}" />
        </view>
      </view>
    </view>

    <!-- 备注信息卡片 -->
    <view class="remark-card" wx:if="{{recordInfo.remark}}">
      <view class="card-title">巡更备注</view>
      <view class="remark-content">{{recordInfo.remark}}</view>
    </view>

    <!-- 空状态提示 -->
    <view wx:if="{{recordInfo.status === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/empty-patrol.png" mode="aspectFit" />
      <text class="empty-text">该任务尚未完成</text>
      <text class="empty-tip">请前往指定地点进行巡更</text>
    </view>
  </view>
</view>
